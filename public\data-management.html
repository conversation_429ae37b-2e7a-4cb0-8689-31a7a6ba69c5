<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据记录与管理 - 小说运营数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .data-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .data-card:hover {
            transform: translateY(-2px);
        }
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .traffic-input {
            max-width: 100px;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-database"></i> 数据记录与管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">返回主页</a>
                <a class="nav-link" href="#record">数据记录</a>
                <a class="nav-link" href="#manage">小说管理</a>
                <a class="nav-link" href="#export">数据导出</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 小说选择和管理 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card data-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-book"></i> 小说选择</h5>
                        <div class="row">
                            <div class="col-md-8">
                                <select class="form-select" id="novelSelect">
                                    <option value="">请选择小说...</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary" onclick="loadNovelData()">
                                    <i class="fas fa-sync"></i> 加载数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card data-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-plus"></i> 小说管理</h5>
                        <button class="btn btn-success w-100 mb-2" onclick="showAddNovelModal()">
                            <i class="fas fa-plus"></i> 添加新小说
                        </button>
                        <button class="btn btn-info w-100" onclick="refreshNovelList()">
                            <i class="fas fa-refresh"></i> 刷新列表
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据记录表单 -->
        <div class="row mb-4" id="dataRecordSection" style="display: none;">
            <div class="col-12">
                <div class="card data-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-edit"></i> 数据记录</h5>
                        
                        <form id="dataRecordForm" class="form-section">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="recordDate" class="form-label">日期</label>
                                    <input type="date" class="form-control" id="recordDate" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" required>
                                        <option value="normal">正常</option>
                                        <option value="abnormal">异常</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="readCount" class="form-label">阅读人数</label>
                                    <input type="number" class="form-control" id="readCount" min="0" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="activeReaders" class="form-label">在读人数</label>
                                    <input type="number" class="form-control" id="activeReaders" min="0" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="rating" class="form-label">作品评分</label>
                                    <input type="number" class="form-control" id="rating" min="0" max="10" step="0.1" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="commentCount" class="form-label">评论次数</label>
                                    <input type="number" class="form-control" id="commentCount" min="0" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="bookmarkCount" class="form-label">加书架人数</label>
                                    <input type="number" class="form-control" id="bookmarkCount" min="0" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="urgeUpdateCount" class="form-label">催更人数</label>
                                    <input type="number" class="form-control" id="urgeUpdateCount" min="0" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="followCount" class="form-label">追更人数</label>
                                    <input type="number" class="form-control" id="followCount" min="0" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="chapterCompletionRate" class="form-label">章节读完率</label>
                                    <input type="number" class="form-control" id="chapterCompletionRate" min="0" max="1" step="0.001" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="chapterFollowRate" class="form-label">章节跟读率</label>
                                    <input type="number" class="form-control" id="chapterFollowRate" min="0" max="1" step="0.001" required>
                                </div>
                            </div>

                            <!-- 字数读完率详细分段 -->
                            <div class="mb-3">
                                <label class="form-label"><i class="fas fa-book-open"></i> 字数读完率分段</label>
                                <div class="row">
                                    <div class="col-md-2">
                                        <label for="wordCompletion10w" class="form-label">10万字读完率</label>
                                        <input type="number" class="form-control" id="wordCompletion10w" min="0" max="1" step="0.001" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="wordCompletion20w" class="form-label">20万字读完率</label>
                                        <input type="number" class="form-control" id="wordCompletion20w" min="0" max="1" step="0.001" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="wordCompletion30w" class="form-label">30万字读完率</label>
                                        <input type="number" class="form-control" id="wordCompletion30w" min="0" max="1" step="0.001" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="wordCompletion50w" class="form-label">50万字读完率</label>
                                        <input type="number" class="form-control" id="wordCompletion50w" min="0" max="1" step="0.001" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="wordCompletion80w" class="form-label">80万字读完率</label>
                                        <input type="number" class="form-control" id="wordCompletion80w" min="0" max="1" step="0.001" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="wordCompletion100w" class="form-label">100万字读完率</label>
                                        <input type="number" class="form-control" id="wordCompletion100w" min="0" max="1" step="0.001" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label"><i class="fas fa-chart-pie"></i> 流量构成</label>
                                <div class="row">
                                    <div class="col-md-2">
                                        <label for="bookstore" class="form-label">书城</label>
                                        <input type="number" class="form-control traffic-input" id="bookstore" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="category" class="form-label">分类</label>
                                        <input type="number" class="form-control traffic-input" id="category" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="bookshelf" class="form-label">书架</label>
                                        <input type="number" class="form-control traffic-input" id="bookshelf" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="continueReading" class="form-label">继续阅读</label>
                                        <input type="number" class="form-control traffic-input" id="continueReading" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="search" class="form-label">搜索</label>
                                        <input type="number" class="form-control traffic-input" id="search" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="other" class="form-label">其他</label>
                                        <input type="number" class="form-control traffic-input" id="other" min="0" required>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-save"></i> 保存数据
                                </button>
                                <button type="button" class="btn btn-secondary me-2" onclick="clearForm()">
                                    <i class="fas fa-eraser"></i> 清空表单
                                </button>
                                <button type="button" class="btn btn-info" onclick="loadTemplate()">
                                    <i class="fas fa-file-alt"></i> 加载模板
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据统计和导出 -->
        <div class="row mb-4" id="dataStatsSection" style="display: none;">
            <div class="col-md-6">
                <div class="card data-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-chart-bar"></i> 数据统计</h5>
                        <div id="statsContent">
                            <p class="text-muted">选择小说后显示统计信息</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card data-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-download"></i> 数据导出</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="exportCSV()">
                                <i class="fas fa-file-csv"></i> 导出CSV格式
                            </button>
                            <button class="btn btn-info" onclick="exportJSON()">
                                <i class="fas fa-file-code"></i> 导出JSON格式
                            </button>
                            <button class="btn btn-warning" onclick="exportExcel()">
                                <i class="fas fa-file-excel"></i> 导出Excel报表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史数据表格 -->
        <div class="row mb-4" id="dataTableSection" style="display: none;">
            <div class="col-12">
                <div class="card data-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0"><i class="fas fa-table"></i> 历史数据记录</h5>
                            <div class="d-flex gap-2">
                                <input type="date" class="form-control" id="filterStartDate" placeholder="开始日期" style="width: 150px;">
                                <input type="date" class="form-control" id="filterEndDate" placeholder="结束日期" style="width: 150px;">
                                <select class="form-select" id="filterStatus" style="width: 120px;">
                                    <option value="">全部状态</option>
                                    <option value="normal">正常</option>
                                    <option value="abnormal">异常</option>
                                </select>
                                <button class="btn btn-outline-primary" onclick="filterData()">
                                    <i class="fas fa-filter"></i> 筛选
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearFilter()">
                                    <i class="fas fa-times"></i> 清除
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="dataTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>日期</th>
                                        <th>状态</th>
                                        <th>阅读人数</th>
                                        <th>在读人数</th>
                                        <th>作品评分</th>
                                        <th>评论次数</th>
                                        <th>加书架人数</th>
                                        <th>催更人数</th>
                                        <th>追更人数</th>
                                        <th>章节读完率</th>
                                        <th>章节跟读率</th>
                                        <th>10万字读完率</th>
                                        <th>20万字读完率</th>
                                        <th>30万字读完率</th>
                                        <th>50万字读完率</th>
                                        <th>80万字读完率</th>
                                        <th>100万字读完率</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="dataTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加小说模态框 -->
    <div class="modal fade" id="addNovelModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加新小说</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addNovelForm">
                        <div class="mb-3">
                            <label for="novelTitle" class="form-label">小说标题 *</label>
                            <input type="text" class="form-control" id="novelTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="novelAuthor" class="form-label">作者 *</label>
                            <input type="text" class="form-control" id="novelAuthor" required>
                        </div>
                        <div class="mb-3">
                            <label for="novelCategory" class="form-label">分类</label>
                            <select class="form-select" id="novelCategory">
                                <option value="都市言情">都市言情</option>
                                <option value="仙侠修真">仙侠修真</option>
                                <option value="科幻小说">科幻小说</option>
                                <option value="历史军事">历史军事</option>
                                <option value="游戏竞技">游戏竞技</option>
                                <option value="悬疑推理">悬疑推理</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="novelPublishDate" class="form-label">发布日期</label>
                            <input type="date" class="form-control" id="novelPublishDate">
                        </div>
                        <div class="mb-3">
                            <label for="novelStatus" class="form-label">状态</label>
                            <select class="form-select" id="novelStatus">
                                <option value="ongoing">连载中</option>
                                <option value="completed">已完结</option>
                                <option value="paused">暂停</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addNovel()">添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑数据模态框 -->
    <div class="modal fade" id="editDataModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑数据记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editDataForm">
                        <input type="hidden" id="editOriginalDate">

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="editDate" class="form-label">日期</label>
                                <input type="date" class="form-control" id="editDate" required>
                            </div>
                            <div class="col-md-3">
                                <label for="editStatus" class="form-label">状态</label>
                                <select class="form-select" id="editStatus" required>
                                    <option value="normal">正常</option>
                                    <option value="abnormal">异常</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="editReadCount" class="form-label">阅读人数</label>
                                <input type="number" class="form-control" id="editReadCount" min="0" required>
                            </div>
                            <div class="col-md-3">
                                <label for="editActiveReaders" class="form-label">在读人数</label>
                                <input type="number" class="form-control" id="editActiveReaders" min="0" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="editRating" class="form-label">作品评分</label>
                                <input type="number" class="form-control" id="editRating" min="0" max="10" step="0.1" required>
                            </div>
                            <div class="col-md-3">
                                <label for="editCommentCount" class="form-label">评论次数</label>
                                <input type="number" class="form-control" id="editCommentCount" min="0" required>
                            </div>
                            <div class="col-md-3">
                                <label for="editBookmarkCount" class="form-label">加书架人数</label>
                                <input type="number" class="form-control" id="editBookmarkCount" min="0" required>
                            </div>
                            <div class="col-md-3">
                                <label for="editUrgeUpdateCount" class="form-label">催更人数</label>
                                <input type="number" class="form-control" id="editUrgeUpdateCount" min="0" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="editFollowCount" class="form-label">追更人数</label>
                                <input type="number" class="form-control" id="editFollowCount" min="0" required>
                            </div>
                            <div class="col-md-3">
                                <label for="editChapterCompletionRate" class="form-label">章节读完率</label>
                                <input type="number" class="form-control" id="editChapterCompletionRate" min="0" max="1" step="0.001" required>
                            </div>
                            <div class="col-md-3">
                                <label for="editChapterFollowRate" class="form-label">章节跟读率</label>
                                <input type="number" class="form-control" id="editChapterFollowRate" min="0" max="1" step="0.001" required>
                            </div>
                        </div>

                        <!-- 字数读完率详细分段 -->
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-book-open"></i> 字数读完率分段</label>
                            <div class="row">
                                <div class="col-md-2">
                                    <label for="editWordCompletion10w" class="form-label">10万字读完率</label>
                                    <input type="number" class="form-control" id="editWordCompletion10w" min="0" max="1" step="0.001" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editWordCompletion20w" class="form-label">20万字读完率</label>
                                    <input type="number" class="form-control" id="editWordCompletion20w" min="0" max="1" step="0.001" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editWordCompletion30w" class="form-label">30万字读完率</label>
                                    <input type="number" class="form-control" id="editWordCompletion30w" min="0" max="1" step="0.001" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editWordCompletion50w" class="form-label">50万字读完率</label>
                                    <input type="number" class="form-control" id="editWordCompletion50w" min="0" max="1" step="0.001" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editWordCompletion80w" class="form-label">80万字读完率</label>
                                    <input type="number" class="form-control" id="editWordCompletion80w" min="0" max="1" step="0.001" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editWordCompletion100w" class="form-label">100万字读完率</label>
                                    <input type="number" class="form-control" id="editWordCompletion100w" min="0" max="1" step="0.001" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-chart-pie"></i> 流量构成</label>
                            <div class="row">
                                <div class="col-md-2">
                                    <label for="editBookstore" class="form-label">书城</label>
                                    <input type="number" class="form-control" id="editBookstore" min="0" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editCategory" class="form-label">分类</label>
                                    <input type="number" class="form-control" id="editCategory" min="0" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editBookshelf" class="form-label">书架</label>
                                    <input type="number" class="form-control" id="editBookshelf" min="0" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editContinueReading" class="form-label">继续阅读</label>
                                    <input type="number" class="form-control" id="editContinueReading" min="0" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editSearch" class="form-label">搜索</label>
                                    <input type="number" class="form-control" id="editSearch" min="0" required>
                                </div>
                                <div class="col-md-2">
                                    <label for="editOther" class="form-label">其他</label>
                                    <input type="number" class="form-control" id="editOther" min="0" required>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateDataRecord()">
                        <i class="fas fa-save"></i> 保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除 <strong id="deleteDate"></strong> 的数据记录吗？</p>
                    <p class="text-danger">此操作不可撤销！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDeleteRecord()">
                        <i class="fas fa-trash"></i> 确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/data-management.js"></script>
</body>
</html>
