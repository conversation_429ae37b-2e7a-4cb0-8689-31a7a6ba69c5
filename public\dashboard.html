<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据面板 - 小说运营数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .dashboard-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .alert-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> 数据面板
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link active" href="dashboard.html">数据面板</a>
                <a class="nav-link" href="prediction.html">AI预测</a>
                <a class="nav-link" href="reports.html">报表导出</a>
                <a class="nav-link" href="data-management.html">数据管理</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h4><i class="fas fa-tachometer-alt"></i> 数据面板</h4>
                    <p class="mb-0">实时监控小说运营数据，关键指标展示，趋势分析图表</p>
                </div>
            </div>
        </div>

        <!-- 小说选择和设置 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-book"></i> 选择小说</h5>
                        <div class="mb-3">
                            <label for="novelSelect" class="form-label">选择小说</label>
                            <select class="form-select" id="novelSelect">
                                <option value="">加载中...</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="loadDashboard()">
                            <i class="fas fa-sync"></i> 加载数据
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-cog"></i> 分析设置</h5>
                        <div class="mb-3">
                            <label for="daysSelect" class="form-label">分析天数</label>
                            <select class="form-select" id="daysSelect">
                                <option value="7">最近7天</option>
                                <option value="15">最近15天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="60">最近60天</option>
                            </select>
                        </div>
                        <button class="btn btn-outline-primary" onclick="refreshData()">
                            <i class="fas fa-refresh"></i> 刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在分析数据，请稍候...</p>
        </div>

        <!-- 数据面板 -->
        <div id="dashboard" style="display: none;">
            <!-- 关键指标 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-eye fa-2x mb-2"></i>
                            <h3 id="totalReads">-</h3>
                            <p>总阅读量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <h3 id="totalSubscribes">-</h3>
                            <p>总订阅量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <h3 id="totalComments">-</h3>
                            <p>总评论数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-heart fa-2x mb-2"></i>
                            <h3 id="totalLikes">-</h3>
                            <p>总点赞数</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-chart-line"></i> 阅读量趋势</h5>
                            <div class="chart-container">
                                <canvas id="readChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-info-circle"></i> 趋势分析</h5>
                            <div id="trendInfo">
                                <p class="text-muted">加载数据后显示趋势分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细数据表格 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-table"></i> 详细数据</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>日期</th>
                                            <th>阅读量</th>
                                            <th>订阅量</th>
                                            <th>评论数</th>
                                            <th>点赞数</th>
                                            <th>评分</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="dataTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">请选择小说并加载数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-bolt"></i> 快速操作</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="prediction.html" class="btn btn-success w-100 mb-2">
                                        <i class="fas fa-robot"></i> AI预测分析
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="reports.html" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-download"></i> 导出报表
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="data-management.html" class="btn btn-warning w-100 mb-2">
                                        <i class="fas fa-plus"></i> 添加数据
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-secondary w-100 mb-2" onclick="refreshData()">
                                        <i class="fas fa-sync"></i> 刷新数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
