<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节数据管理 - 小说运营数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .chapter-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .chapter-card:hover {
            transform: translateY(-2px);
        }
        .chapter-input-group {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .chapter-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
        }
        .table-responsive {
            max-height: 500px;
            overflow-y: auto;
        }
        .chapter-number {
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-book-reader"></i> 章节数据管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">返回主页</a>
                <a class="nav-link" href="data-management.html">数据管理</a>
                <a class="nav-link" href="#chapters">章节分析</a>
                <a class="nav-link" href="#record">数据记录</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 小说选择 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card chapter-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-book"></i> 选择小说</h5>
                        <div class="row">
                            <div class="col-md-8">
                                <select class="form-select" id="novelSelect">
                                    <option value="">请选择小说...</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary" onclick="loadChapterData()">
                                    <i class="fas fa-sync"></i> 加载章节数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card chapter-card">
                    <div class="card-body chapter-stats" id="novelStats">
                        <h6><i class="fas fa-info-circle"></i> 小说信息</h6>
                        <p class="mb-1">选择小说后显示信息</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 章节数据录入 -->
        <div class="row mb-4" id="chapterRecordSection" style="display: none;">
            <div class="col-12">
                <div class="card chapter-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-edit"></i> 章节数据录入</h5>
                        
                        <form id="chapterDataForm">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="recordDate" class="form-label">日期</label>
                                    <input type="date" class="form-control" id="recordDate" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" required>
                                        <option value="normal">正常</option>
                                        <option value="abnormal">异常</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="readCount" class="form-label">总阅读人数</label>
                                    <input type="number" class="form-control" id="readCount" min="0" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="activeReaders" class="form-label">在读人数</label>
                                    <input type="number" class="form-control" id="activeReaders" min="0" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="rating" class="form-label">作品评分</label>
                                    <input type="number" class="form-control" id="rating" min="0" max="10" step="0.1" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="commentCount" class="form-label">评论次数</label>
                                    <input type="number" class="form-control" id="commentCount" min="0" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="bookmarkCount" class="form-label">加书架人数</label>
                                    <input type="number" class="form-control" id="bookmarkCount" min="0" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="wordCompletionRate" class="form-label">字数读完率</label>
                                    <input type="number" class="form-control" id="wordCompletionRate" min="0" max="1" step="0.001" required>
                                </div>
                            </div>

                            <!-- 流量构成 -->
                            <div class="mb-3">
                                <label class="form-label"><i class="fas fa-chart-pie"></i> 流量构成</label>
                                <div class="row">
                                    <div class="col-md-2">
                                        <label for="bookstore" class="form-label">书城</label>
                                        <input type="number" class="form-control" id="bookstore" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="category" class="form-label">分类</label>
                                        <input type="number" class="form-control" id="category" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="bookshelf" class="form-label">书架</label>
                                        <input type="number" class="form-control" id="bookshelf" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="continueReading" class="form-label">继续阅读</label>
                                        <input type="number" class="form-control" id="continueReading" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="search" class="form-label">搜索</label>
                                        <input type="number" class="form-control" id="search" min="0" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="other" class="form-label">其他</label>
                                        <input type="number" class="form-control" id="other" min="0" required>
                                    </div>
                                </div>
                            </div>

                            <!-- 章节数据区域 -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <label class="form-label"><i class="fas fa-list-ol"></i> 章节数据</label>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="addChapter()">
                                            <i class="fas fa-plus"></i> 添加章节
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadChapterTemplate()">
                                            <i class="fas fa-file-alt"></i> 加载模板
                                        </button>
                                    </div>
                                </div>
                                <div id="chaptersContainer">
                                    <!-- 章节输入框将在这里动态生成 -->
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-save"></i> 保存章节数据
                                </button>
                                <button type="button" class="btn btn-secondary me-2" onclick="clearForm()">
                                    <i class="fas fa-eraser"></i> 清空表单
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 章节分析统计 -->
        <div class="row mb-4" id="chapterAnalysisSection" style="display: none;">
            <div class="col-md-6">
                <div class="card chapter-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-chart-bar"></i> 章节统计概览</h5>
                        <div id="chapterSummary">
                            <p class="text-muted">加载章节数据后显示统计信息</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card chapter-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-trophy"></i> 章节排行</h5>
                        <div id="chapterRanking">
                            <p class="text-muted">加载章节数据后显示排行</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 章节数据表格 -->
        <div class="row mb-4" id="chapterTableSection" style="display: none;">
            <div class="col-12">
                <div class="card chapter-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-table"></i> 章节数据详情</h5>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="chapterTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>章节</th>
                                        <th>章节标题</th>
                                        <th>平均读完率</th>
                                        <th>平均跟读率</th>
                                        <th>平均阅读人数</th>
                                        <th>平均阅读时长</th>
                                        <th>数据记录数</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="chapterTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/chapter-management.js"></script>
</body>
</html>
