const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');
const moment = require('moment');

/**
 * 获取所有小说列表
 */
router.get('/novels', async (req, res) => {
    try {
        const novels = dataService.getAllNovels();
        
        res.json({
            success: true,
            data: novels,
            meta: {
                total: novels.length,
                retrievedAt: new Date()
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '获取小说列表失败', 
            message: error.message 
        });
    }
});

/**
 * 添加新小说
 */
router.post('/novels', async (req, res) => {
    try {
        const { title, author, category, publishDate, status } = req.body;
        
        if (!title || !author) {
            return res.status(400).json({ 
                error: '标题和作者为必填项' 
            });
        }
        
        const novelData = {
            title,
            author,
            category: category || '其他',
            publishDate: publishDate || moment().format('YYYY-MM-DD'),
            status: status || 'ongoing'
        };
        
        const novel = dataService.addNovel(novelData);
        
        res.status(201).json({
            success: true,
            data: novel,
            message: '小说添加成功'
        });
    } catch (error) {
        res.status(500).json({ 
            error: '添加小说失败', 
            message: error.message 
        });
    }
});

/**
 * 更新小说信息
 */
router.put('/novels/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const updateData = req.body;
        
        const updatedNovel = dataService.updateNovel(novelId, updateData);
        
        res.json({
            success: true,
            data: updatedNovel,
            message: '小说信息更新成功'
        });
    } catch (error) {
        if (error.message === '小说不存在') {
            res.status(404).json({ error: error.message });
        } else {
            res.status(500).json({ 
                error: '更新小说失败', 
                message: error.message 
            });
        }
    }
});

/**
 * 删除小说
 */
router.delete('/novels/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        
        const deleted = dataService.deleteNovel(novelId);
        
        if (deleted) {
            res.json({
                success: true,
                message: '小说删除成功'
            });
        } else {
            res.status(404).json({ 
                error: '小说不存在' 
            });
        }
    } catch (error) {
        res.status(500).json({ 
            error: '删除小说失败', 
            message: error.message 
        });
    }
});

/**
 * 获取数据记录模板
 */
router.get('/template', async (req, res) => {
    try {
        const { novelId, chapterCount = 5 } = req.query;

        // 如果提供了小说ID，获取小说的章节数
        let actualChapterCount = parseInt(chapterCount);
        if (novelId) {
            const novelInfo = dataService.getNovelInfo(novelId);
            if (novelInfo && novelInfo.currentChapters) {
                actualChapterCount = novelInfo.currentChapters;
            }
        }

        const template = dataService.getDataTemplate(actualChapterCount);

        res.json({
            success: true,
            data: template,
            description: {
                date: '日期 (YYYY-MM-DD)',
                status: '状态 (normal/abnormal)',
                readCount: '总阅读人数',
                activeReaders: '在读人数',
                rating: '作品评分 (0.0-10.0)',
                commentCount: '评论次数',
                bookmarkCount: '加书架人数',
                urgeUpdateCount: '催更人数',
                followCount: '追更人数',
                wordCompletionRate: '字数读完率 (0.000-1.000)',
                chapterData: '各章节详细数据数组',
                'chapterData[].chapterNumber': '章节号',
                'chapterData[].chapterTitle': '章节标题',
                'chapterData[].chapterCompletionRate': '该章节读完率 (0.000-1.000)',
                'chapterData[].chapterFollowRate': '该章节跟读率 (0.000-1.000)',
                trafficSources: {
                    bookstore: '书城流量',
                    category: '分类流量',
                    bookshelf: '书架流量',
                    continueReading: '继续阅读流量',
                    search: '搜索流量',
                    other: '其他流量'
                }
            },
            meta: {
                chapterCount: actualChapterCount,
                novelId: novelId || null
            }
        });
    } catch (error) {
        res.status(500).json({
            error: '获取模板失败',
            message: error.message
        });
    }
});

/**
 * 记录单日数据
 */
router.post('/record/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { date, ...dataRecord } = req.body;
        
        if (!date) {
            return res.status(400).json({ 
                error: '日期为必填项' 
            });
        }
        
        const record = dataService.recordDailyData(novelId, date, dataRecord);
        
        res.status(201).json({
            success: true,
            data: record,
            message: '数据记录成功'
        });
    } catch (error) {
        res.status(400).json({ 
            error: '数据记录失败', 
            message: error.message 
        });
    }
});

/**
 * 批量记录数据
 */
router.post('/batch-record/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { records } = req.body;
        
        if (!Array.isArray(records) || records.length === 0) {
            return res.status(400).json({ 
                error: '请提供有效的数据记录数组' 
            });
        }
        
        const result = dataService.recordBatchData(novelId, records);
        
        res.json({
            success: true,
            data: result,
            message: `成功记录 ${result.results.length} 条数据，失败 ${result.errors.length} 条`
        });
    } catch (error) {
        res.status(500).json({ 
            error: '批量记录失败', 
            message: error.message 
        });
    }
});

/**
 * 导出CSV数据
 */
router.get('/export/csv/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 30 } = req.query;
        
        const csvContent = dataService.exportToCSV(novelId, parseInt(days));
        
        if (!csvContent) {
            return res.status(404).json({ 
                error: '暂无数据可导出' 
            });
        }
        
        const novelInfo = dataService.getNovelInfo(novelId);
        const fileName = `${novelInfo?.title || novelId}_数据记录_${moment().format('YYYYMMDD')}.csv`;
        
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
        
        // 添加BOM以支持Excel正确显示中文
        res.send('\ufeff' + csvContent);
        
    } catch (error) {
        res.status(500).json({ 
            error: 'CSV导出失败', 
            message: error.message 
        });
    }
});

/**
 * 获取数据统计信息
 */
router.get('/stats/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 30 } = req.query;
        
        const historicalData = dataService.getHistoricalData(novelId, parseInt(days));
        
        if (historicalData.length === 0) {
            return res.status(404).json({ 
                error: '暂无统计数据' 
            });
        }
        
        // 计算统计信息
        const stats = {
            totalRecords: historicalData.length,
            dateRange: {
                start: historicalData[0].date,
                end: historicalData[historicalData.length - 1].date
            },
            averages: {
                readCount: Math.round(historicalData.reduce((sum, item) => sum + (item.readCount || 0), 0) / historicalData.length),
                activeReaders: Math.round(historicalData.reduce((sum, item) => sum + (item.activeReaders || 0), 0) / historicalData.length),
                rating: (historicalData.reduce((sum, item) => sum + parseFloat(item.rating || 0), 0) / historicalData.length).toFixed(1),
                commentCount: Math.round(historicalData.reduce((sum, item) => sum + (item.commentCount || 0), 0) / historicalData.length),
                bookmarkCount: Math.round(historicalData.reduce((sum, item) => sum + (item.bookmarkCount || 0), 0) / historicalData.length),
                chapterCompletionRate: (historicalData.reduce((sum, item) => sum + parseFloat(item.chapterCompletionRate || 0), 0) / historicalData.length).toFixed(3),
                chapterFollowRate: (historicalData.reduce((sum, item) => sum + parseFloat(item.chapterFollowRate || 0), 0) / historicalData.length).toFixed(3),
                wordCompletionRate: (historicalData.reduce((sum, item) => sum + parseFloat(item.wordCompletionRate || 0), 0) / historicalData.length).toFixed(3)
            },
            trafficStats: {
                totalTraffic: historicalData.reduce((sum, item) => {
                    const traffic = item.trafficSources || {};
                    return sum + (traffic.bookstore || 0) + (traffic.category || 0) + 
                           (traffic.bookshelf || 0) + (traffic.continueReading || 0) + 
                           (traffic.search || 0) + (traffic.other || 0);
                }, 0),
                averageDistribution: {
                    bookstore: Math.round(historicalData.reduce((sum, item) => sum + (item.trafficSources?.bookstore || 0), 0) / historicalData.length),
                    category: Math.round(historicalData.reduce((sum, item) => sum + (item.trafficSources?.category || 0), 0) / historicalData.length),
                    bookshelf: Math.round(historicalData.reduce((sum, item) => sum + (item.trafficSources?.bookshelf || 0), 0) / historicalData.length),
                    continueReading: Math.round(historicalData.reduce((sum, item) => sum + (item.trafficSources?.continueReading || 0), 0) / historicalData.length),
                    search: Math.round(historicalData.reduce((sum, item) => sum + (item.trafficSources?.search || 0), 0) / historicalData.length),
                    other: Math.round(historicalData.reduce((sum, item) => sum + (item.trafficSources?.other || 0), 0) / historicalData.length)
                }
            },
            statusDistribution: {
                normal: historicalData.filter(item => item.status === 'normal').length,
                abnormal: historicalData.filter(item => item.status === 'abnormal').length
            }
        };
        
        res.json({
            success: true,
            data: stats,
            meta: {
                novelId,
                days: parseInt(days),
                calculatedAt: new Date()
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '获取统计信息失败', 
            message: error.message 
        });
    }
});

/**
 * 获取章节数据分析
 */
router.get('/chapters/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 7 } = req.query;

        const historicalData = dataService.getHistoricalData(novelId, parseInt(days));

        if (historicalData.length === 0) {
            return res.status(404).json({
                error: '暂无章节数据'
            });
        }

        // 分析章节数据
        const chapterAnalysis = {};

        historicalData.forEach(dayData => {
            if (dayData.chapterData && Array.isArray(dayData.chapterData)) {
                dayData.chapterData.forEach(chapter => {
                    const chapterNum = chapter.chapterNumber;

                    if (!chapterAnalysis[chapterNum]) {
                        chapterAnalysis[chapterNum] = {
                            chapterNumber: chapterNum,
                            chapterTitle: chapter.chapterTitle || `第${chapterNum}章`,
                            records: [],
                            averages: {
                                completionRate: 0,
                                followRate: 0
                            }
                        };
                    }

                    chapterAnalysis[chapterNum].records.push({
                        date: dayData.date,
                        completionRate: parseFloat(chapter.chapterCompletionRate || 0),
                        followRate: parseFloat(chapter.chapterFollowRate || 0),
                        readCount: chapter.chapterReadCount || 0,
                        avgReadTime: chapter.chapterAvgReadTime || 0
                    });
                });
            }
        });

        // 计算平均值
        Object.values(chapterAnalysis).forEach(chapter => {
            const records = chapter.records;
            if (records.length > 0) {
                chapter.averages.completionRate = (records.reduce((sum, r) => sum + r.completionRate, 0) / records.length).toFixed(3);
                chapter.averages.followRate = (records.reduce((sum, r) => sum + r.followRate, 0) / records.length).toFixed(3);
                chapter.averages.readCount = Math.round(records.reduce((sum, r) => sum + r.readCount, 0) / records.length);
                chapter.averages.avgReadTime = Math.round(records.reduce((sum, r) => sum + r.avgReadTime, 0) / records.length);
            }
        });

        const sortedChapters = Object.values(chapterAnalysis).sort((a, b) => a.chapterNumber - b.chapterNumber);

        res.json({
            success: true,
            data: {
                chapters: sortedChapters,
                summary: {
                    totalChapters: sortedChapters.length,
                    avgCompletionRate: (sortedChapters.reduce((sum, ch) => sum + parseFloat(ch.averages.completionRate), 0) / sortedChapters.length).toFixed(3),
                    avgFollowRate: (sortedChapters.reduce((sum, ch) => sum + parseFloat(ch.averages.followRate), 0) / sortedChapters.length).toFixed(3),
                    bestChapter: sortedChapters.reduce((best, current) =>
                        parseFloat(current.averages.completionRate) > parseFloat(best.averages.completionRate) ? current : best
                    ),
                    worstChapter: sortedChapters.reduce((worst, current) =>
                        parseFloat(current.averages.completionRate) < parseFloat(worst.averages.completionRate) ? current : worst
                    )
                }
            },
            meta: {
                novelId,
                days: parseInt(days),
                analyzedAt: new Date()
            }
        });
    } catch (error) {
        res.status(500).json({
            error: '章节数据分析失败',
            message: error.message
        });
    }
});

/**
 * 获取单个章节的详细数据
 */
router.get('/chapters/:novelId/:chapterNumber', async (req, res) => {
    try {
        const { novelId, chapterNumber } = req.params;
        const { days = 30 } = req.query;

        const historicalData = dataService.getHistoricalData(novelId, parseInt(days));
        const chapterNum = parseInt(chapterNumber);

        const chapterHistory = [];

        historicalData.forEach(dayData => {
            if (dayData.chapterData && Array.isArray(dayData.chapterData)) {
                const chapterData = dayData.chapterData.find(ch => ch.chapterNumber === chapterNum);
                if (chapterData) {
                    chapterHistory.push({
                        date: dayData.date,
                        ...chapterData
                    });
                }
            }
        });

        if (chapterHistory.length === 0) {
            return res.status(404).json({
                error: `第${chapterNumber}章暂无数据`
            });
        }

        // 计算趋势
        const completionRates = chapterHistory.map(h => parseFloat(h.chapterCompletionRate || 0));
        const followRates = chapterHistory.map(h => parseFloat(h.chapterFollowRate || 0));

        const trend = {
            completionRate: {
                current: completionRates[completionRates.length - 1],
                previous: completionRates[completionRates.length - 2] || completionRates[completionRates.length - 1],
                trend: completionRates[completionRates.length - 1] > (completionRates[completionRates.length - 2] || 0) ? 'up' : 'down'
            },
            followRate: {
                current: followRates[followRates.length - 1],
                previous: followRates[followRates.length - 2] || followRates[followRates.length - 1],
                trend: followRates[followRates.length - 1] > (followRates[followRates.length - 2] || 0) ? 'up' : 'down'
            }
        };

        res.json({
            success: true,
            data: {
                chapterNumber: chapterNum,
                chapterTitle: chapterHistory[0].chapterTitle || `第${chapterNum}章`,
                history: chapterHistory,
                trend: trend,
                averages: {
                    completionRate: (completionRates.reduce((sum, rate) => sum + rate, 0) / completionRates.length).toFixed(3),
                    followRate: (followRates.reduce((sum, rate) => sum + rate, 0) / followRates.length).toFixed(3),
                    readCount: Math.round(chapterHistory.reduce((sum, h) => sum + (h.chapterReadCount || 0), 0) / chapterHistory.length),
                    avgReadTime: Math.round(chapterHistory.reduce((sum, h) => sum + (h.chapterAvgReadTime || 0), 0) / chapterHistory.length)
                }
            },
            meta: {
                novelId,
                chapterNumber: chapterNum,
                days: parseInt(days),
                dataPoints: chapterHistory.length
            }
        });
    } catch (error) {
        res.status(500).json({
            error: '章节详细数据获取失败',
            message: error.message
        });
    }
});

module.exports = router;
