// 全局变量
let currentNovelId = '';
let novels = [];
let chapterCount = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadNovelList();
    setDefaultDate();
});

// 设置默认日期为今天
function setDefaultDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('recordDate').value = today;
}

// 显示成功信息
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>成功:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 显示错误信息
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>错误:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
}

// 加载小说列表
async function loadNovelList() {
    try {
        const response = await axios.get('/api/data/novels');
        novels = response.data.data;
        
        const select = document.getElementById('novelSelect');
        select.innerHTML = '<option value="">请选择小说...</option>';
        
        novels.forEach(novel => {
            const option = document.createElement('option');
            option.value = novel.id;
            option.textContent = `${novel.title} - ${novel.author} (${novel.category})`;
            select.appendChild(option);
        });
        
    } catch (error) {
        showError('加载小说列表失败: ' + (error.response?.data?.message || error.message));
    }
}

// 加载章节数据
async function loadChapterData() {
    const novelId = document.getElementById('novelSelect').value;
    
    if (!novelId) {
        showError('请先选择小说');
        return;
    }
    
    currentNovelId = novelId;
    
    try {
        // 获取小说信息
        const novelResponse = await axios.get(`/api/analysis/novel/${novelId}`);
        const novelInfo = novelResponse.data.data;
        
        // 更新小说信息显示
        updateNovelStats(novelInfo);
        
        // 设置章节数
        chapterCount = novelInfo.currentChapters || 10;
        
        // 显示相关区域
        document.getElementById('chapterRecordSection').style.display = 'block';
        document.getElementById('chapterAnalysisSection').style.display = 'block';
        document.getElementById('chapterTableSection').style.display = 'block';
        
        // 加载章节分析数据
        await loadChapterAnalysis();
        
        // 生成章节输入框
        generateChapterInputs(chapterCount);
        
        showSuccess('章节数据加载完成');
        
    } catch (error) {
        showError('加载章节数据失败: ' + (error.response?.data?.message || error.message));
    }
}

// 更新小说信息显示
function updateNovelStats(novelInfo) {
    const statsDiv = document.getElementById('novelStats');
    statsDiv.innerHTML = `
        <h6><i class="fas fa-book"></i> ${novelInfo.title}</h6>
        <p class="mb-1"><strong>作者:</strong> ${novelInfo.author}</p>
        <p class="mb-1"><strong>分类:</strong> ${novelInfo.category}</p>
        <p class="mb-1"><strong>状态:</strong> ${novelInfo.status === 'ongoing' ? '连载中' : '已完结'}</p>
        <p class="mb-0"><strong>章节数:</strong> ${novelInfo.currentChapters || 'N/A'} / ${novelInfo.totalChapters || 'N/A'}</p>
    `;
}

// 生成章节输入框
function generateChapterInputs(count) {
    const container = document.getElementById('chaptersContainer');
    container.innerHTML = '';

    for (let i = 1; i <= count; i++) {
        const chapterDiv = document.createElement('div');
        chapterDiv.className = 'chapter-input-group';
        chapterDiv.innerHTML = `
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">第${i}章</label>
                    <input type="text" class="form-control" name="chapterTitle_${i}" placeholder="章节标题" value="第${i}章">
                </div>
                <div class="col-md-3">
                    <label class="form-label">读完率</label>
                    <input type="number" class="form-control" name="chapterCompletionRate_${i}" min="0" max="1" step="0.001" placeholder="0.000" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">跟读率</label>
                    <input type="number" class="form-control" name="chapterFollowRate_${i}" min="0" max="1" step="0.001" placeholder="0.000" required>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeChapter(${i})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(chapterDiv);
    }
}

// 添加章节
function addChapter() {
    chapterCount++;
    const container = document.getElementById('chaptersContainer');

    const chapterDiv = document.createElement('div');
    chapterDiv.className = 'chapter-input-group';
    chapterDiv.id = `chapter_${chapterCount}`;
    chapterDiv.innerHTML = `
        <div class="row">
            <div class="col-md-4">
                <label class="form-label">第${chapterCount}章</label>
                <input type="text" class="form-control" name="chapterTitle_${chapterCount}" placeholder="章节标题" value="第${chapterCount}章">
            </div>
            <div class="col-md-3">
                <label class="form-label">读完率</label>
                <input type="number" class="form-control" name="chapterCompletionRate_${chapterCount}" min="0" max="1" step="0.001" placeholder="0.000" required>
            </div>
            <div class="col-md-3">
                <label class="form-label">跟读率</label>
                <input type="number" class="form-control" name="chapterFollowRate_${chapterCount}" min="0" max="1" step="0.001" placeholder="0.000" required>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeChapter(${chapterCount})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(chapterDiv);
}

// 移除章节
function removeChapter(chapterNumber) {
    const chapterDiv = document.getElementById(`chapter_${chapterNumber}`);
    if (chapterDiv) {
        chapterDiv.remove();
    }
}

// 加载章节模板
async function loadChapterTemplate() {
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }
    
    try {
        const response = await axios.get(`/api/data/template?novelId=${currentNovelId}&chapterCount=${chapterCount}`);
        const template = response.data.data;
        
        // 填充基础数据
        document.getElementById('status').value = template.status;
        document.getElementById('readCount').value = template.readCount;
        document.getElementById('activeReaders').value = template.activeReaders;
        document.getElementById('rating').value = template.rating;
        document.getElementById('commentCount').value = template.commentCount;
        document.getElementById('bookmarkCount').value = template.bookmarkCount;
        document.getElementById('wordCompletionRate').value = template.wordCompletionRate;
        
        // 填充流量构成
        document.getElementById('bookstore').value = template.trafficSources.bookstore;
        document.getElementById('category').value = template.trafficSources.category;
        document.getElementById('bookshelf').value = template.trafficSources.bookshelf;
        document.getElementById('continueReading').value = template.trafficSources.continueReading;
        document.getElementById('search').value = template.trafficSources.search;
        document.getElementById('other').value = template.trafficSources.other;
        
        // 填充章节数据
        if (template.chapterData && Array.isArray(template.chapterData)) {
            template.chapterData.forEach(chapter => {
                const chapterNum = chapter.chapterNumber;
                const titleInput = document.querySelector(`input[name="chapterTitle_${chapterNum}"]`);
                const completionInput = document.querySelector(`input[name="chapterCompletionRate_${chapterNum}"]`);
                const followInput = document.querySelector(`input[name="chapterFollowRate_${chapterNum}"]`);

                if (titleInput) titleInput.value = chapter.chapterTitle;
                if (completionInput) completionInput.value = chapter.chapterCompletionRate;
                if (followInput) followInput.value = chapter.chapterFollowRate;
            });
        }
        
        showSuccess('章节模板数据已加载');
        
    } catch (error) {
        showError('加载章节模板失败: ' + (error.response?.data?.message || error.message));
    }
}

// 清空表单
function clearForm() {
    document.getElementById('chapterDataForm').reset();
    setDefaultDate();
    generateChapterInputs(chapterCount);
    showSuccess('表单已清空');
}

// 加载章节分析数据
async function loadChapterAnalysis() {
    try {
        const response = await axios.get(`/api/data/chapters/${currentNovelId}?days=7`);
        const analysisData = response.data.data;
        
        // 更新章节统计概览
        updateChapterSummary(analysisData.summary);
        
        // 更新章节排行
        updateChapterRanking(analysisData.chapters);
        
        // 更新章节数据表格
        updateChapterTable(analysisData.chapters);
        
    } catch (error) {
        document.getElementById('chapterSummary').innerHTML = '<p class="text-danger">章节分析数据加载失败</p>';
        document.getElementById('chapterRanking').innerHTML = '<p class="text-danger">章节排行数据加载失败</p>';
    }
}

// 更新章节统计概览
function updateChapterSummary(summary) {
    const summaryDiv = document.getElementById('chapterSummary');
    summaryDiv.innerHTML = `
        <div class="row">
            <div class="col-6">
                <p><strong>总章节数:</strong> ${summary.totalChapters}</p>
                <p><strong>平均读完率:</strong> ${(summary.avgCompletionRate * 100).toFixed(1)}%</p>
                <p><strong>平均跟读率:</strong> ${(summary.avgFollowRate * 100).toFixed(1)}%</p>
            </div>
            <div class="col-6">
                <p><strong>最佳章节:</strong> 第${summary.bestChapter.chapterNumber}章</p>
                <p><strong>最差章节:</strong> 第${summary.worstChapter.chapterNumber}章</p>
            </div>
        </div>
    `;
}

// 更新章节排行
function updateChapterRanking(chapters) {
    const rankingDiv = document.getElementById('chapterRanking');
    
    // 按读完率排序，取前5名
    const topChapters = chapters
        .sort((a, b) => parseFloat(b.averages.completionRate) - parseFloat(a.averages.completionRate))
        .slice(0, 5);
    
    let rankingHTML = '<div class="list-group list-group-flush">';
    topChapters.forEach((chapter, index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
        rankingHTML += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <span>${medal} 第${chapter.chapterNumber}章</span>
                <span class="badge bg-primary rounded-pill">${(chapter.averages.completionRate * 100).toFixed(1)}%</span>
            </div>
        `;
    });
    rankingHTML += '</div>';
    
    rankingDiv.innerHTML = rankingHTML;
}

// 更新章节数据表格
function updateChapterTable(chapters) {
    const tbody = document.getElementById('chapterTableBody');
    tbody.innerHTML = '';

    chapters.forEach(chapter => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="chapter-number">第${chapter.chapterNumber}章</span></td>
            <td>${chapter.chapterTitle}</td>
            <td>${(chapter.averages.completionRate * 100).toFixed(1)}%</td>
            <td>${(chapter.averages.followRate * 100).toFixed(1)}%</td>
            <td>${chapter.records.length}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewChapterDetail(${chapter.chapterNumber})">
                    <i class="fas fa-eye"></i> 详情
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 查看章节详情
function viewChapterDetail(chapterNumber) {
    // 这里可以实现章节详情查看功能
    showSuccess(`查看第${chapterNumber}章详情功能正在开发中...`);
}

// 提交章节数据表单
document.getElementById('chapterDataForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }
    
    try {
        // 收集章节数据
        const chapterData = [];
        const container = document.getElementById('chaptersContainer');
        const chapterGroups = container.querySelectorAll('.chapter-input-group');
        
        chapterGroups.forEach((group, index) => {
            const chapterNum = index + 1;
            const titleInput = group.querySelector(`input[name="chapterTitle_${chapterNum}"]`);
            const completionInput = group.querySelector(`input[name="chapterCompletionRate_${chapterNum}"]`);
            const followInput = group.querySelector(`input[name="chapterFollowRate_${chapterNum}"]`);

            if (completionInput && followInput && completionInput.value && followInput.value) {
                chapterData.push({
                    chapterNumber: chapterNum,
                    chapterTitle: titleInput ? titleInput.value : `第${chapterNum}章`,
                    chapterCompletionRate: completionInput.value,
                    chapterFollowRate: followInput.value,
                    chapterReadCount: 0, // 设为默认值
                    chapterAvgReadTime: 0 // 设为默认值
                });
            }
        });
        
        if (chapterData.length === 0) {
            showError('请至少填写一个章节的数据');
            return;
        }
        
        // 构建完整数据
        const data = {
            date: document.getElementById('recordDate').value,
            status: document.getElementById('status').value,
            readCount: parseInt(document.getElementById('readCount').value),
            activeReaders: parseInt(document.getElementById('activeReaders').value),
            rating: document.getElementById('rating').value,
            commentCount: parseInt(document.getElementById('commentCount').value),
            bookmarkCount: parseInt(document.getElementById('bookmarkCount').value),
            urgeUpdateCount: 0, // 这里可以添加催更人数字段
            followCount: 0, // 这里可以添加追更人数字段
            wordCompletionRate: document.getElementById('wordCompletionRate').value,
            chapterData: chapterData,
            trafficSources: {
                bookstore: parseInt(document.getElementById('bookstore').value),
                category: parseInt(document.getElementById('category').value),
                bookshelf: parseInt(document.getElementById('bookshelf').value),
                continueReading: parseInt(document.getElementById('continueReading').value),
                search: parseInt(document.getElementById('search').value),
                other: parseInt(document.getElementById('other').value)
            }
        };
        
        await axios.post(`/api/data/record/${currentNovelId}`, data);
        
        showSuccess('章节数据记录成功');
        
        // 刷新分析数据
        await loadChapterAnalysis();
        
        // 清空表单
        clearForm();
        
    } catch (error) {
        showError('章节数据记录失败: ' + (error.response?.data?.message || error.message));
    }
});
