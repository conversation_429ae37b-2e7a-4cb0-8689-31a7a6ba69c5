const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');
const aiService = require('../services/aiService');
const moment = require('moment');

/**
 * 数据预测接口
 */
router.post('/predict/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 7, includeAnalysis = true } = req.body;
        
        // 获取历史数据
        const historicalData = dataService.getHistoricalData(novelId, 30);
        
        if (historicalData.length < 7) {
            return res.status(400).json({ 
                error: '历史数据不足，至少需要7天的数据才能进行预测' 
            });
        }
        
        // 调用AI进行预测
        const predictionResult = await aiService.predictData(historicalData, parseInt(days));
        
        // 保存预测结果
        dataService.savePredictionData(novelId, predictionResult);
        
        // 生成预测日期
        const predictionDates = [];
        for (let i = 1; i <= parseInt(days); i++) {
            predictionDates.push(moment().add(i, 'days').format('YYYY-MM-DD'));
        }
        
        const response = {
            success: true,
            data: {
                novelId,
                predictionPeriod: {
                    startDate: predictionDates[0],
                    endDate: predictionDates[predictionDates.length - 1],
                    days: parseInt(days)
                },
                predictions: predictionResult.predictions,
                analysis: {
                    trendAnalysis: predictionResult.trend_analysis,
                    confidenceLevel: predictionResult.confidence_level,
                    recommendations: predictionResult.recommendations
                },
                historicalContext: {
                    dataPoints: historicalData.length,
                    latestData: historicalData[historicalData.length - 1],
                    averageReads: Math.round(historicalData.reduce((sum, item) => sum + item.readCount, 0) / historicalData.length)
                }
            },
            meta: {
                predictionDate: new Date(),
                modelUsed: 'doubao-1-5-pro-32k-250115',
                basedOnDays: historicalData.length
            }
        };
        
        if (!includeAnalysis) {
            delete response.data.analysis;
        }
        
        res.json(response);
    } catch (error) {
        res.status(500).json({ 
            error: '数据预测失败', 
            message: error.message 
        });
    }
});

/**
 * 获取已保存的预测数据
 */
router.get('/saved/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        
        const savedPrediction = dataService.getPredictionData(novelId);
        
        if (!savedPrediction) {
            return res.status(404).json({ 
                error: '未找到预测数据，请先进行预测' 
            });
        }
        
        // 检查预测数据是否过期
        const isExpired = moment().isAfter(moment(savedPrediction.validUntil));
        
        res.json({
            success: true,
            data: savedPrediction,
            meta: {
                isExpired,
                createdAt: savedPrediction.createdAt,
                validUntil: savedPrediction.validUntil
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '获取预测数据失败', 
            message: error.message 
        });
    }
});

/**
 * 批量预测多个小说
 */
router.post('/batch-predict', async (req, res) => {
    try {
        const { novelIds, days = 7 } = req.body;
        
        if (!Array.isArray(novelIds) || novelIds.length === 0) {
            return res.status(400).json({ error: '请提供小说ID列表' });
        }
        
        const predictions = [];
        const errors = [];
        
        for (const novelId of novelIds) {
            try {
                const historicalData = dataService.getHistoricalData(novelId, 30);
                
                if (historicalData.length < 7) {
                    errors.push({
                        novelId,
                        error: '历史数据不足'
                    });
                    continue;
                }
                
                const predictionResult = await aiService.predictData(historicalData, parseInt(days));
                dataService.savePredictionData(novelId, predictionResult);
                
                predictions.push({
                    novelId,
                    predictions: predictionResult.predictions,
                    confidenceLevel: predictionResult.confidence_level,
                    trendSummary: predictionResult.trend_analysis?.substring(0, 200) + '...'
                });
                
                // 添加延迟避免API限制
                await new Promise(resolve => setTimeout(resolve, 1000));
                
            } catch (error) {
                errors.push({
                    novelId,
                    error: error.message
                });
            }
        }
        
        res.json({
            success: true,
            data: {
                predictions,
                errors,
                summary: {
                    totalRequested: novelIds.length,
                    successful: predictions.length,
                    failed: errors.length
                }
            },
            meta: {
                batchPredictionDate: new Date(),
                days: parseInt(days)
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '批量预测失败', 
            message: error.message 
        });
    }
});

/**
 * 预测准确性验证
 */
router.post('/validate/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { actualData } = req.body; // 实际发生的数据
        
        const savedPrediction = dataService.getPredictionData(novelId);
        
        if (!savedPrediction) {
            return res.status(404).json({ 
                error: '未找到预测数据进行验证' 
            });
        }
        
        // 计算预测准确性
        const predictions = savedPrediction.predictions;
        const validation = {
            totalPredictions: predictions.length,
            validatedPoints: 0,
            accuracyScores: [],
            averageAccuracy: 0,
            details: []
        };
        
        for (let i = 0; i < Math.min(predictions.length, actualData.length); i++) {
            const predicted = predictions[i];
            const actual = actualData[i];
            
            if (predicted && actual && predicted.readCount && actual.readCount) {
                const accuracy = 1 - Math.abs(predicted.readCount - actual.readCount) / actual.readCount;
                const accuracyPercent = Math.max(0, accuracy * 100);
                
                validation.accuracyScores.push(accuracyPercent);
                validation.details.push({
                    date: actual.date,
                    predicted: predicted.readCount,
                    actual: actual.readCount,
                    accuracy: accuracyPercent.toFixed(2) + '%',
                    deviation: predicted.readCount - actual.readCount
                });
                validation.validatedPoints++;
            }
        }
        
        if (validation.accuracyScores.length > 0) {
            validation.averageAccuracy = (
                validation.accuracyScores.reduce((sum, score) => sum + score, 0) / 
                validation.accuracyScores.length
            ).toFixed(2) + '%';
        }
        
        res.json({
            success: true,
            data: validation,
            meta: {
                validationDate: new Date(),
                predictionDate: savedPrediction.createdAt
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '预测验证失败', 
            message: error.message 
        });
    }
});

/**
 * 获取运营策略建议
 */
router.post('/strategy/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        
        // 获取综合数据分析
        const historicalData = dataService.getHistoricalData(novelId, 30);
        const summary = dataService.getDataSummary(novelId, 30);
        const trendAnalysis = dataService.getTrendAnalysis(novelId, 30);
        const predictionData = dataService.getPredictionData(novelId);
        
        const analysisData = {
            novelId,
            summary,
            trendAnalysis,
            recentData: historicalData.slice(-7),
            predictions: predictionData?.predictions || []
        };
        
        const strategyRecommendations = await aiService.generateStrategy(analysisData);
        
        res.json({
            success: true,
            data: {
                novelId,
                recommendations: strategyRecommendations,
                basedOn: {
                    historicalDays: historicalData.length,
                    hasPredictions: !!predictionData,
                    analysisDate: new Date()
                }
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '策略建议生成失败', 
            message: error.message 
        });
    }
});

/**
 * 简化的预测接口（用于前端页面）
 */
router.post('/generate', async (req, res) => {
    try {
        const { novelId, days = 7 } = req.body;

        if (!novelId) {
            return res.status(400).json({
                error: '小说ID为必填项'
            });
        }

        // 获取历史数据
        const historicalData = dataService.getHistoricalData(novelId, 30);

        if (historicalData.length < 3) {
            return res.status(400).json({
                error: '历史数据不足，至少需要3天的数据才能进行预测'
            });
        }

        // 生成模拟预测数据
        const prediction = generateSimplePrediction(historicalData, days);

        res.json({
            success: true,
            data: prediction,
            meta: {
                novelId,
                predictDays: days,
                basedOnDays: historicalData.length,
                generatedAt: new Date()
            }
        });

    } catch (error) {
        res.status(500).json({
            error: '预测生成失败',
            message: error.message
        });
    }
});

/**
 * 生成简化的预测数据
 */
function generateSimplePrediction(historicalData, days) {
    // 计算历史数据的平均值和趋势
    const recentData = historicalData.slice(-7); // 最近7天
    const avgReadCount = Math.round(recentData.reduce((sum, item) => sum + (item.readCount || 0), 0) / recentData.length);
    const avgSubscribeCount = Math.round(recentData.reduce((sum, item) => sum + (item.subscribeCount || 0), 0) / recentData.length);
    const avgCommentCount = Math.round(recentData.reduce((sum, item) => sum + (item.commentCount || 0), 0) / recentData.length);
    const avgRating = (recentData.reduce((sum, item) => sum + parseFloat(item.rating || 0), 0) / recentData.length).toFixed(1);

    // 计算趋势
    const firstHalf = recentData.slice(0, Math.floor(recentData.length / 2));
    const secondHalf = recentData.slice(Math.floor(recentData.length / 2));

    const firstAvg = firstHalf.reduce((sum, item) => sum + (item.readCount || 0), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, item) => sum + (item.readCount || 0), 0) / secondHalf.length;

    let trend = 'stable';
    let trendText = '稳定';
    if (secondAvg > firstAvg * 1.1) {
        trend = 'up';
        trendText = '上升';
    } else if (secondAvg < firstAvg * 0.9) {
        trend = 'down';
        trendText = '下降';
    }

    // 生成预测数据
    const predictions = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1);

    for (let i = 0; i < days; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);

        // 添加随机波动
        const readCountVariation = (Math.random() - 0.5) * 0.2;
        const subscribeVariation = (Math.random() - 0.5) * 0.3;
        const commentVariation = (Math.random() - 0.5) * 0.4;
        const ratingVariation = (Math.random() - 0.5) * 0.2;

        // 应用趋势影响
        let trendFactor = 1;
        if (trend === 'up') {
            trendFactor = 1 + (i * 0.02);
        } else if (trend === 'down') {
            trendFactor = 1 - (i * 0.015);
        }

        const predictedReadCount = Math.max(0, Math.round(avgReadCount * trendFactor * (1 + readCountVariation)));
        const predictedSubscribeCount = Math.max(0, Math.round(avgSubscribeCount * trendFactor * (1 + subscribeVariation)));
        const predictedCommentCount = Math.max(0, Math.round(avgCommentCount * trendFactor * (1 + commentVariation)));
        const predictedRating = Math.max(0, Math.min(10, parseFloat(avgRating) + ratingVariation)).toFixed(1);

        predictions.push({
            date: date.toISOString().split('T')[0],
            readCount: predictedReadCount,
            subscribeCount: predictedSubscribeCount,
            commentCount: predictedCommentCount,
            rating: predictedRating,
            trend: trend
        });
    }

    // 生成洞察和分析
    const insights = [
        trend === 'up' ? '数据呈现上升趋势，运营效果良好' : trend === 'down' ? '数据呈现下降趋势，需要调整运营策略' : '数据保持稳定，运营状态良好',
        '建议关注用户反馈，持续优化内容质量',
        '可以考虑在高峰时段增加推广投入'
    ];

    const analysis = `基于历史${historicalData.length}天的数据分析，预测未来${days}天的数据趋势为${trendText}。建议根据预测结果调整运营策略，保持数据的稳定增长。`;

    // 计算置信度
    const confidence = Math.min(95, Math.max(60, 75 + (historicalData.length > 14 ? 15 : 0) + (trend === 'stable' ? 10 : 0)));

    return {
        period: `${days}天预测`,
        trend: trend,
        trendText: trendText,
        confidence: confidence,
        predictions: predictions,
        insights: insights,
        analysis: analysis
    };
}

module.exports = router;
