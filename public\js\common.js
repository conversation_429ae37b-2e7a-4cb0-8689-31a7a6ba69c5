/**
 * 小说运营数据分析系统 - 通用功能模块
 */

// 页面配置
const PAGE_CONFIG = {
    'index.html': { title: '首页', icon: 'fas fa-home' },
    'dashboard.html': { title: '数据面板', icon: 'fas fa-chart-line' },
    'prediction.html': { title: 'AI预测', icon: 'fas fa-robot' },
    'reports.html': { title: '报表导出', icon: 'fas fa-file-export' },
    'data-management.html': { title: '数据管理', icon: 'fas fa-database' }
};

// 获取当前页面
function getCurrentPage() {
    const path = window.location.pathname;
    const page = path.split('/').pop() || 'index.html';
    return page;
}

// 设置活跃导航项
function setActiveNavItem() {
    const currentPage = getCurrentPage();
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === 'index.html' && href === '/')) {
            link.classList.add('active');
        }
    });
}

// 显示加载状态
function showLoading(elementId, message = '加载中...') {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center p-4">
                <div class="loading mb-3"></div>
                <p class="text-muted">${message}</p>
            </div>
        `;
    }
}

// 隐藏加载状态
function hideLoading(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = '';
    }
}

// 显示错误信息
function showError(elementId, message = '加载失败，请重试') {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
    }
}

// 显示成功信息
function showSuccess(elementId, message = '操作成功') {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> ${message}
            </div>
        `;
    }
}

// 格式化数字
function formatNumber(num) {
    if (num === null || num === undefined) return '-';
    return parseInt(num).toLocaleString();
}

// 格式化百分比
function formatPercentage(num, decimals = 1) {
    if (num === null || num === undefined) return '-';
    return (parseFloat(num) * 100).toFixed(decimals) + '%';
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            return true;
        } catch (err) {
            return false;
        } finally {
            document.body.removeChild(textArea);
        }
    }
}

// 显示提示信息
function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = document.getElementById('toastContainer') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show`;
    toast.style.cssText = 'position: relative; margin-bottom: 0.5rem;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    toastContainer.appendChild(toast);
    
    // 自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, duration);
}

// 创建提示容器
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 350px;
    `;
    document.body.appendChild(container);
    return container;
}

// 确认对话框
function confirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 响应式表格处理
function makeTableResponsive() {
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        if (!table.parentNode.classList.contains('table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
}

// 初始化通用功能
function initCommon() {
    // 设置活跃导航项
    setActiveNavItem();
    
    // 响应式表格
    makeTableResponsive();
    
    // 添加返回顶部按钮
    addBackToTopButton();
    
    // 初始化工具提示
    initTooltips();
}

// 添加返回顶部按钮
function addBackToTopButton() {
    const button = document.createElement('button');
    button.innerHTML = '<i class="fas fa-arrow-up"></i>';
    button.className = 'btn btn-primary';
    button.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: none;
        box-shadow: var(--shadow-medium);
    `;
    button.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
    
    document.body.appendChild(button);
    
    // 滚动显示/隐藏
    window.addEventListener('scroll', throttle(() => {
        if (window.pageYOffset > 300) {
            button.style.display = 'block';
        } else {
            button.style.display = 'none';
        }
    }, 100));
}

// 初始化工具提示
function initTooltips() {
    // 如果Bootstrap已加载，初始化tooltips
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initCommon);

// 导出全局函数
window.CommonUtils = {
    getCurrentPage,
    setActiveNavItem,
    showLoading,
    hideLoading,
    showError,
    showSuccess,
    formatNumber,
    formatPercentage,
    formatDate,
    debounce,
    throttle,
    copyToClipboard,
    showToast,
    confirmDialog,
    makeTableResponsive
};
