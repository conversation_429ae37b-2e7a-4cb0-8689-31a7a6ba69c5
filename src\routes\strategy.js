const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');

/**
 * 生成运营策略建议
 */
router.post('/generate', async (req, res) => {
    try {
        const { novelId, type = 'comprehensive', depth = 'detailed' } = req.body;
        
        if (!novelId) {
            return res.status(400).json({ 
                error: '小说ID为必填项' 
            });
        }
        
        // 获取历史数据
        const historicalData = dataService.getHistoricalData(novelId, 30);
        
        if (historicalData.length < 3) {
            return res.status(400).json({ 
                error: '历史数据不足，至少需要3天的数据才能生成策略' 
            });
        }
        
        // 生成策略建议
        const strategy = generateStrategyRecommendations(historicalData, type, depth);
        
        res.json({
            success: true,
            data: strategy,
            meta: {
                novelId,
                strategyType: type,
                analysisDepth: depth,
                basedOnDays: historicalData.length,
                generatedAt: new Date()
            }
        });
        
    } catch (error) {
        res.status(500).json({ 
            error: '策略生成失败', 
            message: error.message 
        });
    }
});

/**
 * 生成策略建议
 */
function generateStrategyRecommendations(historicalData, type, depth) {
    // 分析数据趋势
    const recentData = historicalData.slice(-7);
    const avgReadCount = recentData.reduce((sum, item) => sum + (item.readCount || 0), 0) / recentData.length;
    const avgRating = recentData.reduce((sum, item) => sum + parseFloat(item.rating || 0), 0) / recentData.length;
    const avgCommentCount = recentData.reduce((sum, item) => sum + (item.commentCount || 0), 0) / recentData.length;
    
    // 计算趋势
    const firstHalf = recentData.slice(0, Math.floor(recentData.length / 2));
    const secondHalf = recentData.slice(Math.floor(recentData.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, item) => sum + (item.readCount || 0), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, item) => sum + (item.readCount || 0), 0) / secondHalf.length;
    
    let trend = 'stable';
    if (secondAvg > firstAvg * 1.1) {
        trend = 'up';
    } else if (secondAvg < firstAvg * 0.9) {
        trend = 'down';
    }
    
    // 根据策略类型生成建议
    let strategies = [];
    let overview = '';
    
    switch (type) {
        case 'comprehensive':
            strategies = generateComprehensiveStrategy(trend, avgRating, avgReadCount, avgCommentCount);
            overview = '基于数据分析的综合运营策略，涵盖内容优化、推广策略、用户互动等多个方面。';
            break;
        case 'content':
            strategies = generateContentStrategy(trend, avgRating, avgCommentCount);
            overview = '专注于内容质量提升的策略建议，帮助改善作品质量和用户满意度。';
            break;
        case 'promotion':
            strategies = generatePromotionStrategy(trend, avgReadCount);
            overview = '针对推广和流量获取的策略建议，帮助扩大作品影响力和读者群体。';
            break;
        case 'engagement':
            strategies = generateEngagementStrategy(avgCommentCount, avgRating);
            overview = '专注于用户互动和社区建设的策略建议，提升用户粘性和活跃度。';
            break;
        default:
            strategies = generateComprehensiveStrategy(trend, avgRating, avgReadCount, avgCommentCount);
            overview = '基于数据分析的综合运营策略。';
    }
    
    // 生成警告和注意事项
    const warnings = generateWarnings(trend, avgRating, historicalData.length);
    
    // 计算置信度
    const confidence = calculateStrategyConfidence(historicalData, type);
    
    return {
        overview: overview,
        strategies: strategies,
        warnings: warnings,
        confidence: confidence,
        analysisDepth: depth,
        dataQuality: assessDataQuality(historicalData)
    };
}

/**
 * 生成综合策略
 */
function generateComprehensiveStrategy(trend, avgRating, avgReadCount, avgCommentCount) {
    const strategies = [];
    
    // 基于趋势的策略
    if (trend === 'up') {
        strategies.push({
            title: '保持增长势头',
            description: '当前数据呈上升趋势，建议保持现有运营策略，并适度扩大推广投入以维持增长。',
            priority: 'high',
            expectedResult: '维持或提升当前增长率'
        });
    } else if (trend === 'down') {
        strategies.push({
            title: '扭转下降趋势',
            description: '数据呈下降趋势，需要立即分析原因并调整策略。建议重新评估内容质量和推广方式。',
            priority: 'high',
            expectedResult: '停止下降并恢复增长'
        });
    } else {
        strategies.push({
            title: '突破稳定瓶颈',
            description: '数据保持稳定，建议尝试新的运营方式来突破现有瓶颈，实现新的增长。',
            priority: 'medium',
            expectedResult: '实现数据突破性增长'
        });
    }
    
    // 基于评分的策略
    if (avgRating < 7.0) {
        strategies.push({
            title: '提升内容质量',
            description: '作品评分偏低，建议重点关注内容质量，收集用户反馈，改进创作方向。',
            priority: 'high',
            expectedResult: '评分提升至7.5以上'
        });
    } else if (avgRating >= 8.5) {
        strategies.push({
            title: '利用高评分优势',
            description: '作品评分较高，建议利用这一优势扩大推广，吸引更多读者。',
            priority: 'medium',
            expectedResult: '在保持高评分的同时扩大读者群'
        });
    }
    
    // 基于互动的策略
    if (avgCommentCount < 10) {
        strategies.push({
            title: '增强用户互动',
            description: '用户互动较少，建议增加与读者的互动，如回复评论、举办活动等。',
            priority: 'medium',
            expectedResult: '评论数量提升50%'
        });
    }
    
    return strategies;
}

/**
 * 生成内容策略
 */
function generateContentStrategy(trend, avgRating, avgCommentCount) {
    const strategies = [];
    
    if (avgRating < 7.5) {
        strategies.push({
            title: '内容质量诊断',
            description: '进行全面的内容质量分析，识别读者不满意的具体方面，制定改进计划。',
            priority: 'high',
            expectedResult: '明确内容改进方向'
        });
        
        strategies.push({
            title: '情节优化',
            description: '重新审视故事情节，确保逻辑合理、节奏适当，增加吸引力。',
            priority: 'high',
            expectedResult: '提升故事吸引力'
        });
    }
    
    strategies.push({
        title: '更新频率优化',
        description: '分析最佳更新时间和频率，保持读者期待感。',
        priority: 'medium',
        expectedResult: '提升读者粘性'
    });
    
    if (avgCommentCount > 20) {
        strategies.push({
            title: '读者反馈整合',
            description: '积极收集和分析读者反馈，将有价值的建议融入创作中。',
            priority: 'medium',
            expectedResult: '提升读者满意度'
        });
    }
    
    return strategies;
}

/**
 * 生成推广策略
 */
function generatePromotionStrategy(trend, avgReadCount) {
    const strategies = [];
    
    if (avgReadCount < 500) {
        strategies.push({
            title: '基础推广建设',
            description: '建立基础的推广渠道，包括社交媒体、论坛等，扩大作品曝光度。',
            priority: 'high',
            expectedResult: '阅读量提升至1000+'
        });
    } else if (avgReadCount < 2000) {
        strategies.push({
            title: '精准推广投放',
            description: '针对目标读者群体进行精准推广，提高转化率。',
            priority: 'high',
            expectedResult: '阅读量翻倍增长'
        });
    } else {
        strategies.push({
            title: '品牌化推广',
            description: '建立作品品牌，通过口碑营销和KOL合作扩大影响力。',
            priority: 'medium',
            expectedResult: '建立稳定的读者群体'
        });
    }
    
    strategies.push({
        title: '多平台分发',
        description: '将作品分发到多个平台，扩大覆盖面。',
        priority: 'medium',
        expectedResult: '增加30%的曝光量'
    });
    
    return strategies;
}

/**
 * 生成用户互动策略
 */
function generateEngagementStrategy(avgCommentCount, avgRating) {
    const strategies = [];
    
    strategies.push({
        title: '建立读者社群',
        description: '创建专门的读者交流群，增强读者归属感和粘性。',
        priority: 'high',
        expectedResult: '建立活跃的读者社群'
    });
    
    if (avgCommentCount < 15) {
        strategies.push({
            title: '激励评论互动',
            description: '通过回复评论、举办讨论活动等方式鼓励读者参与互动。',
            priority: 'high',
            expectedResult: '评论数量提升100%'
        });
    }
    
    strategies.push({
        title: '定期读者活动',
        description: '定期举办问答、投票、征文等活动，保持读者活跃度。',
        priority: 'medium',
        expectedResult: '提升读者活跃度'
    });
    
    return strategies;
}

/**
 * 生成警告信息
 */
function generateWarnings(trend, avgRating, dataLength) {
    const warnings = [];
    
    if (dataLength < 7) {
        warnings.push('数据量较少，策略建议的准确性可能受限，建议积累更多数据后重新分析。');
    }
    
    if (trend === 'down') {
        warnings.push('数据呈下降趋势，需要立即采取行动，避免进一步恶化。');
    }
    
    if (avgRating < 6.0) {
        warnings.push('作品评分过低，可能存在严重的内容质量问题，建议优先解决。');
    }
    
    warnings.push('策略实施需要时间见效，建议持续监控数据变化并适时调整。');
    
    return warnings;
}

/**
 * 计算策略置信度
 */
function calculateStrategyConfidence(historicalData, type) {
    let confidence = 70; // 基础置信度
    
    // 数据量影响
    if (historicalData.length >= 30) {
        confidence += 15;
    } else if (historicalData.length >= 14) {
        confidence += 10;
    } else if (historicalData.length >= 7) {
        confidence += 5;
    }
    
    // 策略类型影响
    if (type === 'comprehensive') {
        confidence += 5; // 综合策略更可靠
    }
    
    return Math.min(90, confidence);
}

/**
 * 评估数据质量
 */
function assessDataQuality(historicalData) {
    if (historicalData.length >= 30) {
        return '优秀';
    } else if (historicalData.length >= 14) {
        return '良好';
    } else if (historicalData.length >= 7) {
        return '一般';
    } else {
        return '不足';
    }
}

module.exports = router;
