// 全局变量
let currentNovelId = '';
let novels = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadNovelList();
});

// 显示成功信息
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>成功:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 显示错误信息
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>错误:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
}

// 加载小说列表
async function loadNovelList() {
    try {
        const response = await axios.get('/api/data/novels');
        novels = response.data.data;
        
        const select = document.getElementById('novelSelect');
        select.innerHTML = '<option value="">请选择小说...</option>';
        
        novels.forEach(novel => {
            const option = document.createElement('option');
            option.value = novel.id;
            option.textContent = `${novel.title} - ${novel.author} (${novel.category})`;
            select.appendChild(option);
        });
        
    } catch (error) {
        showError('加载小说列表失败: ' + (error.response?.data?.message || error.message));
    }
}

// 显示加载状态
function showLoading() {
    document.getElementById('loadingAI').style.display = 'block';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingAI').style.display = 'none';
}

// 生成AI预测
async function generatePrediction() {
    const novelId = document.getElementById('novelSelect').value;
    const predictDays = document.getElementById('predictDays').value;
    
    if (!novelId) {
        showError('请先选择小说');
        return;
    }
    
    currentNovelId = novelId;
    
    try {
        showLoading();
        
        const response = await axios.post('/api/prediction/generate', {
            novelId: novelId,
            days: parseInt(predictDays)
        });
        
        const prediction = response.data.data;
        
        // 更新预测结果显示
        updatePredictionDisplay(prediction, predictDays);
        
        // 显示相关区域
        document.getElementById('predictionSection').style.display = 'block';
        document.getElementById('accuracySection').style.display = 'block';
        document.getElementById('historySection').style.display = 'block';
        
        // 更新准确性信息
        updateAccuracyInfo(prediction.confidence);
        
        // 添加到历史记录
        addToHistory('prediction', predictDays, prediction.confidence);
        
        showSuccess('AI预测生成成功');
        
    } catch (error) {
        showError('AI预测失败: ' + (error.response?.data?.message || error.message));
    } finally {
        hideLoading();
    }
}

// 更新预测结果显示
function updatePredictionDisplay(prediction, days) {
    const badge = document.getElementById('predictionBadge');
    badge.textContent = `${days}天预测`;
    
    const resultDiv = document.getElementById('predictionResult');
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-6">
                <h6><i class="fas fa-chart-line"></i> 预测摘要</h6>
                <p><strong>预测期间:</strong> ${prediction.period}</p>
                <p><strong>数据趋势:</strong> <span class="badge ${prediction.trend === 'up' ? 'bg-success' : prediction.trend === 'down' ? 'bg-danger' : 'bg-warning'}">${prediction.trendText}</span></p>
                <p><strong>置信度:</strong> <span class="badge bg-primary">${prediction.confidence}%</span></p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-exclamation-circle"></i> 关键洞察</h6>
                <ul class="list-unstyled">
    `;
    
    prediction.insights.forEach(insight => {
        html += `<li><i class="fas fa-arrow-right text-primary"></i> ${insight}</li>`;
    });
    
    html += `
                </ul>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-striped prediction-table">
                <thead class="table-dark">
                    <tr>
                        <th>日期</th>
                        <th>预测阅读量</th>
                        <th>预测订阅量</th>
                        <th>预测评论数</th>
                        <th>预测评分</th>
                        <th>趋势</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    prediction.predictions.forEach(pred => {
        html += `
            <tr>
                <td>${pred.date}</td>
                <td>${pred.readCount}</td>
                <td>${pred.subscribeCount}</td>
                <td>${pred.commentCount}</td>
                <td>${pred.rating}</td>
                <td><i class="fas fa-arrow-${pred.trend === 'up' ? 'up text-success' : pred.trend === 'down' ? 'down text-danger' : 'right text-warning'}"></i></td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            <h6><i class="fas fa-lightbulb"></i> AI分析建议</h6>
            <div class="alert alert-info">
                ${prediction.analysis}
            </div>
        </div>
    `;
    
    resultDiv.innerHTML = html;
}

// 生成运营策略
async function generateStrategy() {
    const novelId = document.getElementById('novelSelect').value;
    const strategyType = document.getElementById('strategyType').value;
    const analysisDepth = document.getElementById('analysisDepth').value;
    
    if (!novelId) {
        showError('请先选择小说');
        return;
    }
    
    try {
        showLoading();
        
        const response = await axios.post('/api/strategy/generate', {
            novelId: novelId,
            type: strategyType,
            depth: analysisDepth
        });
        
        const strategy = response.data.data;
        
        // 更新策略结果显示
        updateStrategyDisplay(strategy, strategyType);
        
        // 显示策略区域
        document.getElementById('strategySection').style.display = 'block';
        
        // 添加到历史记录
        addToHistory('strategy', strategyType, strategy.confidence);
        
        showSuccess('运营策略生成成功');
        
    } catch (error) {
        showError('策略生成失败: ' + (error.response?.data?.message || error.message));
    } finally {
        hideLoading();
    }
}

// 更新策略结果显示
function updateStrategyDisplay(strategy, type) {
    const badge = document.getElementById('strategyBadge');
    const typeNames = {
        'comprehensive': '综合策略',
        'content': '内容优化',
        'promotion': '推广策略',
        'engagement': '用户互动'
    };
    badge.textContent = typeNames[type] || '综合策略';
    
    const resultDiv = document.getElementById('strategyResult');
    
    let html = `
        <div class="row mb-3">
            <div class="col-md-12">
                <h6><i class="fas fa-target"></i> 策略概述</h6>
                <p>${strategy.overview}</p>
            </div>
        </div>
    `;
    
    strategy.strategies.forEach((item, index) => {
        html += `
            <div class="strategy-item">
                <h6><i class="fas fa-lightbulb"></i> ${item.title}</h6>
                <p>${item.description}</p>
                <div class="row">
                    <div class="col-md-6">
                        <small><strong>优先级:</strong> <span class="badge ${item.priority === 'high' ? 'bg-danger' : item.priority === 'medium' ? 'bg-warning' : 'bg-success'}">${item.priority === 'high' ? '高' : item.priority === 'medium' ? '中' : '低'}</span></small>
                    </div>
                    <div class="col-md-6">
                        <small><strong>预期效果:</strong> ${item.expectedResult}</small>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
        <div class="mt-3">
            <h6><i class="fas fa-exclamation-triangle"></i> 注意事项</h6>
            <div class="alert alert-warning">
                ${strategy.warnings.join('<br>')}
            </div>
        </div>
    `;
    
    resultDiv.innerHTML = html;
}

// 更新准确性信息
function updateAccuracyInfo(confidence) {
    document.getElementById('confidenceLevel').textContent = `${confidence}%`;
    document.getElementById('confidenceBar').style.width = `${confidence}%`;
    
    // 根据置信度设置颜色
    const bar = document.getElementById('confidenceBar');
    if (confidence >= 80) {
        bar.className = 'progress-bar bg-success';
    } else if (confidence >= 60) {
        bar.className = 'progress-bar bg-warning';
    } else {
        bar.className = 'progress-bar bg-danger';
    }
}

// 添加到历史记录
function addToHistory(type, value, confidence) {
    const tbody = document.getElementById('historyTableBody');
    
    // 如果是第一条记录，清空占位文本
    if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
        tbody.innerHTML = '';
    }
    
    const row = document.createElement('tr');
    const now = new Date();
    const typeText = type === 'prediction' ? '数据预测' : '策略建议';
    const valueText = type === 'prediction' ? `${value}天` : value;
    
    row.innerHTML = `
        <td>${now.toLocaleString()}</td>
        <td>${valueText}</td>
        <td>${typeText}</td>
        <td><span class="badge ${confidence >= 80 ? 'bg-success' : confidence >= 60 ? 'bg-warning' : 'bg-danger'}">${confidence}%</span></td>
        <td><span class="badge bg-success">已完成</span></td>
        <td>
            <button class="btn btn-sm btn-outline-primary" onclick="viewHistoryDetail('${type}', '${value}')">
                <i class="fas fa-eye"></i>
            </button>
        </td>
    `;
    
    tbody.insertBefore(row, tbody.firstChild);
}

// 查看历史详情
function viewHistoryDetail(type, value) {
    showSuccess(`查看${type === 'prediction' ? '预测' : '策略'}详情功能正在开发中...`);
}

// 清空结果
function clearResults() {
    document.getElementById('predictionSection').style.display = 'none';
    document.getElementById('strategySection').style.display = 'none';
    document.getElementById('accuracySection').style.display = 'none';
    document.getElementById('historySection').style.display = 'none';
    
    // 重置表单
    document.getElementById('novelSelect').value = '';
    document.getElementById('predictDays').value = '7';
    document.getElementById('strategyType').value = 'comprehensive';
    document.getElementById('analysisDepth').value = 'detailed';
    
    showSuccess('结果已清空');
}
