// 全局变量
let currentNovelId = '';
let novels = [];
let allDataRecords = []; // 存储所有数据记录用于筛选

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadNovelList();
    setDefaultDate();
});

// 设置默认日期为今天
function setDefaultDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('recordDate').value = today;
    
    const publishDateInput = document.getElementById('novelPublishDate');
    if (publishDateInput) {
        publishDateInput.value = today;
    }
}

// 显示成功信息
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>成功:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 显示错误信息
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>错误:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
}

// 加载小说列表
async function loadNovelList() {
    try {
        const response = await axios.get('/api/data/novels');
        novels = response.data.data;
        
        const select = document.getElementById('novelSelect');
        select.innerHTML = '<option value="">请选择小说...</option>';
        
        novels.forEach(novel => {
            const option = document.createElement('option');
            option.value = novel.id;
            option.textContent = `${novel.title} - ${novel.author} (${novel.category})`;
            select.appendChild(option);
        });
        
    } catch (error) {
        showError('加载小说列表失败: ' + (error.response?.data?.message || error.message));
    }
}

// 刷新小说列表
function refreshNovelList() {
    loadNovelList();
    showSuccess('小说列表已刷新');
}

// 显示添加小说模态框
function showAddNovelModal() {
    const modal = new bootstrap.Modal(document.getElementById('addNovelModal'));
    modal.show();
}

// 添加新小说
async function addNovel() {
    try {
        const title = document.getElementById('novelTitle').value;
        const author = document.getElementById('novelAuthor').value;
        const category = document.getElementById('novelCategory').value;
        const publishDate = document.getElementById('novelPublishDate').value;
        const status = document.getElementById('novelStatus').value;
        
        if (!title || !author) {
            showError('标题和作者为必填项');
            return;
        }
        
        const response = await axios.post('/api/data/novels', {
            title,
            author,
            category,
            publishDate,
            status
        });
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addNovelModal'));
        modal.hide();
        
        // 清空表单
        document.getElementById('addNovelForm').reset();
        setDefaultDate();
        
        // 刷新列表
        await loadNovelList();
        
        showSuccess(`小说《${title}》添加成功`);
        
    } catch (error) {
        showError('添加小说失败: ' + (error.response?.data?.message || error.message));
    }
}

// 加载小说数据
async function loadNovelData() {
    const novelId = document.getElementById('novelSelect').value;
    
    if (!novelId) {
        showError('请先选择小说');
        return;
    }
    
    currentNovelId = novelId;
    
    try {
        // 显示相关区域
        document.getElementById('dataRecordSection').style.display = 'block';
        document.getElementById('dataStatsSection').style.display = 'block';
        document.getElementById('dataTableSection').style.display = 'block';
        
        // 加载统计信息
        await loadStats();
        
        // 加载历史数据表格
        await loadDataTable();
        
        showSuccess('数据加载完成');
        
    } catch (error) {
        showError('加载数据失败: ' + (error.response?.data?.message || error.message));
    }
}

// 加载统计信息
async function loadStats() {
    try {
        const response = await axios.get(`/api/data/stats/${currentNovelId}?days=30`);
        const stats = response.data.data;
        
        const statsContent = document.getElementById('statsContent');
        statsContent.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <p><strong>数据记录:</strong> ${stats.totalRecords} 条</p>
                    <p><strong>日期范围:</strong> ${stats.dateRange.start} 至 ${stats.dateRange.end}</p>
                    <p><strong>平均阅读人数:</strong> ${stats.averages.readCount}</p>
                    <p><strong>平均在读人数:</strong> ${stats.averages.activeReaders}</p>
                    <p><strong>平均评分:</strong> ${stats.averages.rating}</p>
                </div>
                <div class="col-6">
                    <p><strong>平均评论数:</strong> ${stats.averages.commentCount}</p>
                    <p><strong>平均书架数:</strong> ${stats.averages.bookmarkCount}</p>
                    <p><strong>章节读完率:</strong> ${(stats.averages.chapterCompletionRate * 100).toFixed(1)}%</p>
                    <p><strong>章节跟读率:</strong> ${(stats.averages.chapterFollowRate * 100).toFixed(1)}%</p>
                    <p><strong>字数读完率:</strong> ${(stats.averages.wordCompletionRate * 100).toFixed(1)}%</p>
                </div>
            </div>
            <div class="mt-3">
                <h6>流量构成平均值:</h6>
                <div class="row">
                    <div class="col-4">书城: ${stats.trafficStats.averageDistribution.bookstore}</div>
                    <div class="col-4">分类: ${stats.trafficStats.averageDistribution.category}</div>
                    <div class="col-4">书架: ${stats.trafficStats.averageDistribution.bookshelf}</div>
                    <div class="col-4">继续阅读: ${stats.trafficStats.averageDistribution.continueReading}</div>
                    <div class="col-4">搜索: ${stats.trafficStats.averageDistribution.search}</div>
                    <div class="col-4">其他: ${stats.trafficStats.averageDistribution.other}</div>
                </div>
            </div>
        `;
        
    } catch (error) {
        document.getElementById('statsContent').innerHTML = '<p class="text-danger">统计信息加载失败</p>';
    }
}

// 加载数据表格
async function loadDataTable() {
    try {
        const response = await axios.get(`/api/analysis/historical/${currentNovelId}?days=60`);
        allDataRecords = response.data.data;

        renderDataTable(allDataRecords);

    } catch (error) {
        document.getElementById('dataTableBody').innerHTML = '<tr><td colspan="13" class="text-center text-danger">数据加载失败</td></tr>';
    }
}

// 渲染数据表格
function renderDataTable(data) {
    const tbody = document.getElementById('dataTableBody');
    tbody.innerHTML = '';

    if (data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="13" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    data.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.date}</td>
            <td><span class="badge ${item.status === 'normal' ? 'bg-success' : 'bg-warning'}">${item.status === 'normal' ? '正常' : '异常'}</span></td>
            <td>${item.readCount || 0}</td>
            <td>${item.activeReaders || 0}</td>
            <td>${item.rating || '0.0'}</td>
            <td>${item.commentCount || 0}</td>
            <td>${item.bookmarkCount || 0}</td>
            <td>${item.urgeUpdateCount || 0}</td>
            <td>${item.followCount || 0}</td>
            <td>${((item.chapterCompletionRate || 0) * 100).toFixed(1)}%</td>
            <td>${((item.chapterFollowRate || 0) * 100).toFixed(1)}%</td>
            <td>${((item.wordCompletionRate || 0) * 100).toFixed(1)}%</td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="editRecord('${item.date}')" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteRecord('${item.date}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 加载数据模板
async function loadTemplate() {
    try {
        const response = await axios.get('/api/data/template');
        const template = response.data.data;
        
        // 填充表单
        document.getElementById('status').value = template.status;
        document.getElementById('readCount').value = template.readCount;
        document.getElementById('activeReaders').value = template.activeReaders;
        document.getElementById('rating').value = template.rating;
        document.getElementById('commentCount').value = template.commentCount;
        document.getElementById('bookmarkCount').value = template.bookmarkCount;
        document.getElementById('urgeUpdateCount').value = template.urgeUpdateCount;
        document.getElementById('followCount').value = template.followCount;
        document.getElementById('chapterCompletionRate').value = template.chapterCompletionRate;
        document.getElementById('chapterFollowRate').value = template.chapterFollowRate;
        document.getElementById('wordCompletionRate').value = template.wordCompletionRate;
        
        // 填充流量构成
        document.getElementById('bookstore').value = template.trafficSources.bookstore;
        document.getElementById('category').value = template.trafficSources.category;
        document.getElementById('bookshelf').value = template.trafficSources.bookshelf;
        document.getElementById('continueReading').value = template.trafficSources.continueReading;
        document.getElementById('search').value = template.trafficSources.search;
        document.getElementById('other').value = template.trafficSources.other;
        
        showSuccess('模板数据已加载');
        
    } catch (error) {
        showError('加载模板失败: ' + (error.response?.data?.message || error.message));
    }
}

// 清空表单
function clearForm() {
    document.getElementById('dataRecordForm').reset();
    setDefaultDate();
    showSuccess('表单已清空');
}

// 提交数据记录表单
document.getElementById('dataRecordForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }
    
    try {
        const formData = new FormData(this);
        const data = {
            date: document.getElementById('recordDate').value,
            status: document.getElementById('status').value,
            readCount: parseInt(document.getElementById('readCount').value),
            activeReaders: parseInt(document.getElementById('activeReaders').value),
            rating: document.getElementById('rating').value,
            commentCount: parseInt(document.getElementById('commentCount').value),
            bookmarkCount: parseInt(document.getElementById('bookmarkCount').value),
            urgeUpdateCount: parseInt(document.getElementById('urgeUpdateCount').value),
            followCount: parseInt(document.getElementById('followCount').value),
            chapterCompletionRate: document.getElementById('chapterCompletionRate').value,
            chapterFollowRate: document.getElementById('chapterFollowRate').value,
            wordCompletionRate: document.getElementById('wordCompletionRate').value,
            trafficSources: {
                bookstore: parseInt(document.getElementById('bookstore').value),
                category: parseInt(document.getElementById('category').value),
                bookshelf: parseInt(document.getElementById('bookshelf').value),
                continueReading: parseInt(document.getElementById('continueReading').value),
                search: parseInt(document.getElementById('search').value),
                other: parseInt(document.getElementById('other').value)
            }
        };
        
        await axios.post(`/api/data/record/${currentNovelId}`, data);
        
        showSuccess('数据记录成功');
        
        // 刷新统计和表格
        await loadStats();
        await loadDataTable();
        
        // 清空表单
        clearForm();
        
    } catch (error) {
        showError('数据记录失败: ' + (error.response?.data?.message || error.message));
    }
});

// 导出CSV
function exportCSV() {
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }
    
    const url = `/api/data/export/csv/${currentNovelId}?days=30`;
    window.open(url, '_blank');
    showSuccess('CSV文件导出中...');
}

// 导出JSON
async function exportJSON() {
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }
    
    try {
        const response = await axios.get(`/api/analysis/historical/${currentNovelId}?days=30`);
        const data = response.data.data;
        
        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `${currentNovelId}_数据记录_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        showSuccess('JSON数据导出完成');
        
    } catch (error) {
        showError('JSON导出失败: ' + (error.response?.data?.message || error.message));
    }
}

// 导出Excel
function exportExcel() {
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }
    
    const url = `/api/report/excel/${currentNovelId}?days=30&includePredict=false`;
    window.open(url, '_blank');
    showSuccess('Excel报表导出中...');
}

// 编辑记录
async function editRecord(date) {
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }

    try {
        // 获取记录详情
        const response = await axios.get(`/api/data/record/${currentNovelId}/${date}`);
        const record = response.data.data;

        // 填充编辑表单
        document.getElementById('editOriginalDate').value = date;
        document.getElementById('editDate').value = record.date;
        document.getElementById('editStatus').value = record.status;
        document.getElementById('editReadCount').value = record.readCount;
        document.getElementById('editActiveReaders').value = record.activeReaders;
        document.getElementById('editRating').value = record.rating;
        document.getElementById('editCommentCount').value = record.commentCount;
        document.getElementById('editBookmarkCount').value = record.bookmarkCount;
        document.getElementById('editUrgeUpdateCount').value = record.urgeUpdateCount;
        document.getElementById('editFollowCount').value = record.followCount;
        document.getElementById('editChapterCompletionRate').value = record.chapterCompletionRate;
        document.getElementById('editChapterFollowRate').value = record.chapterFollowRate;
        document.getElementById('editWordCompletionRate').value = record.wordCompletionRate;

        // 填充流量构成
        document.getElementById('editBookstore').value = record.trafficSources.bookstore;
        document.getElementById('editCategory').value = record.trafficSources.category;
        document.getElementById('editBookshelf').value = record.trafficSources.bookshelf;
        document.getElementById('editContinueReading').value = record.trafficSources.continueReading;
        document.getElementById('editSearch').value = record.trafficSources.search;
        document.getElementById('editOther').value = record.trafficSources.other;

        // 显示编辑模态框
        const modal = new bootstrap.Modal(document.getElementById('editDataModal'));
        modal.show();

    } catch (error) {
        showError('获取记录详情失败: ' + (error.response?.data?.message || error.message));
    }
}

// 更新数据记录
async function updateDataRecord() {
    try {
        const originalDate = document.getElementById('editOriginalDate').value;
        const newDate = document.getElementById('editDate').value;

        const data = {
            status: document.getElementById('editStatus').value,
            readCount: parseInt(document.getElementById('editReadCount').value),
            activeReaders: parseInt(document.getElementById('editActiveReaders').value),
            rating: document.getElementById('editRating').value,
            commentCount: parseInt(document.getElementById('editCommentCount').value),
            bookmarkCount: parseInt(document.getElementById('editBookmarkCount').value),
            urgeUpdateCount: parseInt(document.getElementById('editUrgeUpdateCount').value),
            followCount: parseInt(document.getElementById('editFollowCount').value),
            chapterCompletionRate: document.getElementById('editChapterCompletionRate').value,
            chapterFollowRate: document.getElementById('editChapterFollowRate').value,
            wordCompletionRate: document.getElementById('editWordCompletionRate').value,
            trafficSources: {
                bookstore: parseInt(document.getElementById('editBookstore').value),
                category: parseInt(document.getElementById('editCategory').value),
                bookshelf: parseInt(document.getElementById('editBookshelf').value),
                continueReading: parseInt(document.getElementById('editContinueReading').value),
                search: parseInt(document.getElementById('editSearch').value),
                other: parseInt(document.getElementById('editOther').value)
            }
        };

        // 如果日期改变了，需要先删除旧记录，再创建新记录
        if (originalDate !== newDate) {
            // 删除旧记录
            await axios.delete(`/api/data/record/${currentNovelId}/${originalDate}`);
            // 创建新记录
            await axios.post(`/api/data/record/${currentNovelId}`, { date: newDate, ...data });
        } else {
            // 更新现有记录
            await axios.put(`/api/data/record/${currentNovelId}/${originalDate}`, data);
        }

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('editDataModal'));
        modal.hide();

        showSuccess('数据更新成功');

        // 刷新数据
        await loadStats();
        await loadDataTable();

    } catch (error) {
        showError('数据更新失败: ' + (error.response?.data?.message || error.message));
    }
}

// 删除记录
function deleteRecord(date) {
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }

    // 设置删除日期并显示确认模态框
    document.getElementById('deleteDate').textContent = date;

    // 保存要删除的日期到全局变量
    window.deleteTargetDate = date;

    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

// 确认删除记录
async function confirmDeleteRecord() {
    try {
        const date = window.deleteTargetDate;

        await axios.delete(`/api/data/record/${currentNovelId}/${date}`);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();

        showSuccess('数据删除成功');

        // 刷新数据
        await loadStats();
        await loadDataTable();

    } catch (error) {
        showError('数据删除失败: ' + (error.response?.data?.message || error.message));
    }
}

// 筛选数据
function filterData() {
    const startDate = document.getElementById('filterStartDate').value;
    const endDate = document.getElementById('filterEndDate').value;
    const status = document.getElementById('filterStatus').value;

    let filteredData = [...allDataRecords];

    // 按日期筛选
    if (startDate) {
        filteredData = filteredData.filter(item => item.date >= startDate);
    }
    if (endDate) {
        filteredData = filteredData.filter(item => item.date <= endDate);
    }

    // 按状态筛选
    if (status) {
        filteredData = filteredData.filter(item => item.status === status);
    }

    // 按日期降序排序
    filteredData.sort((a, b) => new Date(b.date) - new Date(a.date));

    renderDataTable(filteredData);

    showSuccess(`筛选完成，共找到 ${filteredData.length} 条记录`);
}

// 清除筛选
function clearFilter() {
    document.getElementById('filterStartDate').value = '';
    document.getElementById('filterEndDate').value = '';
    document.getElementById('filterStatus').value = '';

    // 按日期降序排序显示所有数据
    const sortedData = [...allDataRecords].sort((a, b) => new Date(b.date) - new Date(a.date));
    renderDataTable(sortedData);

    showSuccess('筛选已清除');
}
