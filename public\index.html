<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小说运营数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .dashboard-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .alert-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-book-open"></i> 小说运营数据分析系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#dashboard">数据面板</a>
                <a class="nav-link" href="#prediction">AI预测</a>
                <a class="nav-link" href="#reports">报表导出</a>
                <a class="nav-link" href="data-management.html">数据管理</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎区域 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h4><i class="fas fa-chart-line"></i> 欢迎使用小说运营数据分析系统</h4>
                    <p class="mb-0">配合AI自动预测，对运营数据进行诊断、记录和统计，提供专业的数据分析和运营建议。</p>
                </div>
            </div>
        </div>

        <!-- 小说选择 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-book"></i> 选择小说</h5>
                        <div class="mb-3">
                            <label for="novelSelect" class="form-label">选择小说</label>
                            <select class="form-select" id="novelSelect">
                                <option value="">加载中...</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="loadDashboard()">
                            <i class="fas fa-sync"></i> 加载数据
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card dashboard-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-cog"></i> 分析设置</h5>
                        <div class="mb-3">
                            <label for="daysSelect" class="form-label">分析天数</label>
                            <select class="form-select" id="daysSelect">
                                <option value="7">最近7天</option>
                                <option value="15">最近15天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="60">最近60天</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="predictDays" class="form-label">预测天数</label>
                            <select class="form-select" id="predictDays">
                                <option value="3">3天</option>
                                <option value="7" selected>7天</option>
                                <option value="14">14天</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在分析数据，请稍候...</p>
        </div>

        <!-- 数据面板 -->
        <div id="dashboard" style="display: none;">
            <!-- 关键指标 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-eye fa-2x mb-2"></i>
                            <h3 id="totalReads">-</h3>
                            <p>总阅读量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <h3 id="totalSubscribes">-</h3>
                            <p>总订阅量</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <h3 id="totalComments">-</h3>
                            <p>总评论数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <i class="fas fa-heart fa-2x mb-2"></i>
                            <h3 id="totalLikes">-</h3>
                            <p>总点赞数</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-chart-line"></i> 阅读量趋势</h5>
                            <div class="chart-container">
                                <canvas id="readChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-info-circle"></i> 趋势分析</h5>
                            <div id="trendInfo">
                                <p class="text-muted">加载数据后显示趋势分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI预测区域 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-robot"></i> AI数据预测
                                </h5>
                                <button class="btn btn-primary btn-sm" onclick="generatePrediction()">
                                    <i class="fas fa-magic"></i> 生成预测
                                </button>
                            </div>
                            <div id="predictionResult">
                                <p class="text-muted">点击"生成预测"按钮开始AI分析</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 运营建议 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-lightbulb"></i> 运营策略建议
                                </h5>
                                <button class="btn btn-primary btn-sm" onclick="generateStrategy()">
                                    <i class="fas fa-brain"></i> 生成建议
                                </button>
                            </div>
                            <div id="strategyResult">
                                <p class="text-muted">点击"生成建议"按钮获取AI运营建议</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报表导出 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card dashboard-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-download"></i> 报表导出</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <button class="btn btn-success w-100" onclick="exportExcel()">
                                        <i class="fas fa-file-excel"></i> 导出Excel报表
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-info w-100" onclick="exportHTML()">
                                        <i class="fas fa-file-code"></i> 导出HTML报表
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-warning w-100" onclick="exportJSON()">
                                        <i class="fas fa-file-alt"></i> 导出JSON数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/dashboard.js"></script>
</body>
</html>
