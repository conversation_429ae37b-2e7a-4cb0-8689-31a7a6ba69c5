<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小说运营数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <style>
        .hero-section {
            background: var(--primary-gradient);
            color: white;
            padding: 4rem 0;
            text-align: center;
            border-radius: var(--border-radius-lg);
            margin: 2rem 0;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .feature-card {
            height: 100%;
            border: none;
            transition: var(--transition);
            background: var(--light-color);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-heavy);
        }

        .feature-icon {
            background: var(--primary-gradient);
            color: white;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .feature-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .stats-section {
            background: var(--light-color);
            border-radius: var(--border-radius-lg);
            padding: 3rem 2rem;
            margin: 3rem 0;
            box-shadow: var(--shadow-light);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            text-align: center;
            margin-bottom: 3rem;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-section {
                padding: 3rem 0;
                margin: 1rem 0;
            }

            .feature-icon {
                font-size: 1.8rem;
                width: 70px;
                height: 70px;
            }

            .section-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-section {
                padding: 2rem 0;
            }

            .feature-card {
                margin-bottom: 1.5rem;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-book-open"></i> 小说运营数据分析系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="dashboard.html">数据面板</a>
                    <a class="nav-link" href="prediction.html">AI预测</a>
                    <a class="nav-link" href="reports.html">报表导出</a>
                    <a class="nav-link" href="data-management.html">数据管理</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="hero-title">小说运营数据分析系统</h1>
            <p class="hero-subtitle">配合AI自动预测，对运营数据进行诊断、记录和统计，提供专业的数据分析和运营建议</p>
        </div>
    </div>

    <div class="container">
        <!-- 功能模块 -->
        <div class="row mb-5">
            <div class="col-md-3 mb-4">
                <div class="card feature-card text-center">
                    <div class="card-body p-4">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5 class="feature-title">数据面板</h5>
                        <p class="feature-description">实时数据监控，关键指标展示，趋势分析图表</p>
                        <a href="dashboard.html" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> 进入面板
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card feature-card text-center">
                    <div class="card-body p-4">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h5 class="feature-title">AI预测</h5>
                        <p class="feature-description">智能数据预测，未来趋势分析，运营策略建议</p>
                        <a href="prediction.html" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> AI分析
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card feature-card text-center">
                    <div class="card-body p-4">
                        <div class="feature-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <h5 class="feature-title">报表导出</h5>
                        <p class="feature-description">多格式报表导出，数据可视化，专业分析报告</p>
                        <a href="reports.html" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> 导出报表
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card feature-card text-center">
                    <div class="card-body p-4">
                        <div class="feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h5 class="feature-title">数据管理</h5>
                        <p class="feature-description">数据录入管理，历史记录查询，CRUD操作</p>
                        <a href="data-management.html" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> 管理数据
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统特色 -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="section-title">系统特色</h2>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-brain text-primary"></i> AI智能分析</h5>
                        <p class="card-text">配合豆包AI进行智能数据预测和分析，提供专业的运营策略建议。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-chart-bar text-success"></i> 数据可视化</h5>
                        <p class="card-text">丰富的图表展示，直观的数据分析，让数据变化一目了然。</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-cogs text-info"></i> 完整CRUD</h5>
                        <p class="card-text">支持数据的增删改查操作，历史数据管理，数据筛选功能。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速开始 -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h3 class="card-title">快速开始</h3>
                        <p class="card-text">选择一个功能模块开始您的数据分析之旅</p>
                        <div class="row">
                            <div class="col-md-3">
                                <a href="dashboard.html" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-tachometer-alt"></i><br>数据面板
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="prediction.html" class="btn btn-outline-success w-100 mb-2">
                                    <i class="fas fa-magic"></i><br>AI预测
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="reports.html" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-file-export"></i><br>报表导出
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="data-management.html" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="fas fa-database"></i><br>数据管理
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container text-center">
            <p class="text-muted mb-0">© 2025 小说运营数据分析系统 - 配合AI自动预测，专业数据分析</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/common.js"></script>
</body>
</html>
