/* 小说运营数据分析系统 - 统一简约风格样式 */

/* 全局变量 */
:root {
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #f8f9fa;
    --accent-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #ffffff;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.08);
    --shadow-medium: 0 4px 20px rgba(0,0,0,0.12);
    --shadow-heavy: 0 8px 30px rgba(0,0,0,0.15);
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏简约风格 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
    font-size: 1.25rem;
    text-decoration: none;
}

.nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    margin: 0 0.25rem;
}

.nav-link:hover {
    background: var(--secondary-color);
    color: var(--primary-color) !important;
}

.nav-link.active {
    background: var(--primary-gradient);
    color: white !important;
}

/* 卡片简约风格 */
.card {
    background: var(--light-color);
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.card-header {
    background: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 按钮简约风格 */
.btn {
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-success {
    background: var(--accent-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--dark-color);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* 表单简约风格 */
.form-control, .form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: var(--light-color);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* 表格简约风格 */
.table {
    background: var(--light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table th {
    background: var(--secondary-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* 指标卡片 */
.metric-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    overflow: hidden;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.metric-card .card-body {
    text-align: center;
    padding: 2rem 1.5rem;
}

.metric-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.metric-card p {
    font-size: 0.9rem;
    opacity: 0.9;
    margin: 0;
}

/* 警告框简约风格 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: var(--accent-color);
    color: var(--accent-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
    color: #856404;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
    color: var(--info-color);
}

/* 徽章简约风格 */
.badge {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
    
    .metric-card .card-body {
        padding: 1.5rem 1rem;
    }
    
    .metric-card h3 {
        font-size: 1.5rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .navbar-nav {
        padding: 1rem 0;
    }
    
    .nav-link {
        padding: 0.5rem 1rem !important;
        margin: 0.25rem 0;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 0.75rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    .metric-card .card-body {
        padding: 1rem 0.75rem;
    }
    
    .metric-card h3 {
        font-size: 1.25rem;
    }
    
    .metric-card p {
        font-size: 0.8rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 页面标题区域 */
.page-header {
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.page-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* 工具提示 */
.tooltip-inner {
    background: var(--dark-color);
    border-radius: var(--border-radius-sm);
}

/* 进度条 */
.progress {
    height: 8px;
    border-radius: var(--border-radius-sm);
    background: var(--secondary-color);
}

.progress-bar {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
}
