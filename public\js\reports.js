// 全局变量
let currentNovelId = '';
let novels = [];
let downloadHistory = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadNovelList();
    loadDownloadHistory();
});

// 显示成功信息
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>成功:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 显示错误信息
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>错误:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
}

// 加载小说列表
async function loadNovelList() {
    try {
        const response = await axios.get('/api/data/novels');
        novels = response.data.data;
        
        const select = document.getElementById('novelSelect');
        select.innerHTML = '<option value="">请选择小说...</option>';
        
        novels.forEach(novel => {
            const option = document.createElement('option');
            option.value = novel.id;
            option.textContent = `${novel.title} - ${novel.author} (${novel.category})`;
            select.appendChild(option);
        });
        
    } catch (error) {
        showError('加载小说列表失败: ' + (error.response?.data?.message || error.message));
    }
}

// 导出报表
async function exportReport(format) {
    const novelId = document.getElementById('novelSelect').value;
    const dateRange = document.getElementById('dateRange').value;
    const includeCharts = document.getElementById('includeCharts').checked;
    const includePrediction = document.getElementById('includePrediction').checked;
    const includeStrategy = document.getElementById('includeStrategy').checked;
    const reportTemplate = document.getElementById('reportTemplate').value;
    
    if (!novelId) {
        showError('请先选择小说');
        return;
    }
    
    currentNovelId = novelId;
    
    try {
        // 显示加载状态
        const button = event.target.closest('.format-card').querySelector('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
        button.disabled = true;
        
        // 构建导出参数
        const params = {
            days: dateRange,
            includeCharts: includeCharts,
            includePrediction: includePrediction,
            includeStrategy: includeStrategy,
            template: reportTemplate
        };
        
        let url = '';
        let filename = '';
        const novel = novels.find(n => n.id === novelId);
        const novelTitle = novel ? novel.title : novelId;
        
        switch (format) {
            case 'excel':
                url = `/api/report/excel/${novelId}`;
                filename = `${novelTitle}_数据报表_${new Date().toISOString().split('T')[0]}.xlsx`;
                break;
            case 'html':
                url = `/api/report/html/${novelId}`;
                filename = `${novelTitle}_数据报表_${new Date().toISOString().split('T')[0]}.html`;
                break;
            case 'csv':
                url = `/api/data/export/csv/${novelId}`;
                filename = `${novelTitle}_数据记录_${new Date().toISOString().split('T')[0]}.csv`;
                break;
            case 'json':
                url = `/api/report/data/${novelId}`;
                filename = `${novelTitle}_数据_${new Date().toISOString().split('T')[0]}.json`;
                break;
            case 'pdf':
                url = `/api/report/pdf/${novelId}`;
                filename = `${novelTitle}_分析报告_${new Date().toISOString().split('T')[0]}.pdf`;
                break;
            default:
                throw new Error('不支持的导出格式');
        }
        
        // 发起下载请求
        const response = await axios.get(url, {
            params: params,
            responseType: 'blob'
        });
        
        // 创建下载链接
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        
        // 添加到下载历史
        addToDownloadHistory(novelTitle, format, dateRange, '成功');
        
        showSuccess(`${format.toUpperCase()}报表导出成功`);
        
    } catch (error) {
        // 添加失败记录到历史
        const novel = novels.find(n => n.id === novelId);
        const novelTitle = novel ? novel.title : novelId;
        addToDownloadHistory(novelTitle, format, dateRange, '失败');
        
        showError(`导出失败: ${error.response?.data?.message || error.message}`);
    } finally {
        // 恢复按钮状态
        const button = event.target.closest('.format-card').querySelector('button');
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// 预览报表
async function previewReport() {
    const novelId = document.getElementById('novelSelect').value;
    const dateRange = document.getElementById('dateRange').value;
    const reportTemplate = document.getElementById('reportTemplate').value;
    
    if (!novelId) {
        showError('请先选择小说');
        return;
    }
    
    try {
        // 显示加载状态
        const button = event.target.closest('.format-card').querySelector('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
        button.disabled = true;
        
        const response = await axios.get(`/api/report/preview/${novelId}`, {
            params: {
                days: dateRange,
                template: reportTemplate
            }
        });
        
        const previewData = response.data.data;
        
        // 显示预览区域
        document.getElementById('previewSection').style.display = 'block';
        
        // 更新预览内容
        updatePreviewDisplay(previewData);
        
        showSuccess('报表预览生成成功');
        
    } catch (error) {
        showError(`预览失败: ${error.response?.data?.message || error.message}`);
    } finally {
        // 恢复按钮状态
        const button = event.target.closest('.format-card').querySelector('button');
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// 更新预览显示
function updatePreviewDisplay(previewData) {
    const previewDiv = document.getElementById('reportPreview');
    
    const novel = novels.find(n => n.id === currentNovelId);
    const novelTitle = novel ? novel.title : currentNovelId;
    
    let html = `
        <div class="text-center mb-4">
            <h4>${novelTitle} - 数据分析报表</h4>
            <p class="text-muted">报表生成时间: ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-primary">${previewData.summary.totalReads}</h5>
                        <small>总阅读量</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-success">${previewData.summary.totalSubscribes}</h5>
                        <small>总订阅量</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-info">${previewData.summary.totalComments}</h5>
                        <small>总评论数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="text-warning">${previewData.summary.avgRating}</h5>
                        <small>平均评分</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>日期</th>
                        <th>阅读量</th>
                        <th>订阅量</th>
                        <th>评论数</th>
                        <th>评分</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    previewData.recentData.forEach(item => {
        html += `
            <tr>
                <td>${item.date}</td>
                <td>${item.readCount || 0}</td>
                <td>${item.subscribeCount || 0}</td>
                <td>${item.commentCount || 0}</td>
                <td>${item.rating || '0.0'}</td>
                <td><span class="badge ${item.status === 'normal' ? 'bg-success' : 'bg-warning'}">${item.status === 'normal' ? '正常' : '异常'}</span></td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
        
        <div class="mt-4">
            <h6>趋势分析</h6>
            <div class="alert alert-info">
                ${previewData.analysis || '数据趋势稳定，建议继续保持当前运营策略。'}
            </div>
        </div>
    `;
    
    previewDiv.innerHTML = html;
}

// 添加到下载历史
function addToDownloadHistory(novelTitle, format, dateRange, status) {
    const historyItem = {
        time: new Date().toLocaleString(),
        novel: novelTitle,
        format: format.toUpperCase(),
        range: `最近${dateRange}天`,
        status: status
    };
    
    downloadHistory.unshift(historyItem);
    
    // 限制历史记录数量
    if (downloadHistory.length > 20) {
        downloadHistory = downloadHistory.slice(0, 20);
    }
    
    updateDownloadHistoryDisplay();
    saveDownloadHistory();
}

// 更新下载历史显示
function updateDownloadHistoryDisplay() {
    const tbody = document.getElementById('downloadHistoryBody');
    
    if (downloadHistory.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无下载记录</td></tr>';
        return;
    }
    
    tbody.innerHTML = '';
    
    downloadHistory.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.time}</td>
            <td>${item.novel}</td>
            <td><span class="badge bg-secondary">${item.format}</span></td>
            <td>${item.range}</td>
            <td><span class="badge ${item.status === '成功' ? 'bg-success' : 'bg-danger'}">${item.status}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-danger" onclick="removeHistoryItem(${index})" title="删除">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 删除历史记录项
function removeHistoryItem(index) {
    downloadHistory.splice(index, 1);
    updateDownloadHistoryDisplay();
    saveDownloadHistory();
    showSuccess('历史记录已删除');
}

// 清空下载历史
function clearHistory() {
    if (downloadHistory.length === 0) {
        showError('没有历史记录可清空');
        return;
    }
    
    if (confirm('确定要清空所有下载历史吗？')) {
        downloadHistory = [];
        updateDownloadHistoryDisplay();
        saveDownloadHistory();
        showSuccess('下载历史已清空');
    }
}

// 保存下载历史到本地存储
function saveDownloadHistory() {
    localStorage.setItem('downloadHistory', JSON.stringify(downloadHistory));
}

// 加载下载历史
function loadDownloadHistory() {
    const saved = localStorage.getItem('downloadHistory');
    if (saved) {
        downloadHistory = JSON.parse(saved);
        updateDownloadHistoryDisplay();
    }
}

// 刷新数据
function refreshData() {
    loadNovelList();
    showSuccess('数据已刷新');
}
