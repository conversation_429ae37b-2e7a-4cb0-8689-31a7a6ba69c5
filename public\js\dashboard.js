// 全局变量
let currentNovelId = 'novel_001';
let currentData = null;
let readChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadNovelList();
});

// 显示加载状态
function showLoading() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('dashboard').style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
    document.getElementById('dashboard').style.display = 'block';
}

// 显示错误信息
function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>错误:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
}

// 显示成功信息
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <strong>成功:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

// 加载小说列表
async function loadNovelList() {
    try {
        const response = await axios.get('/api/data/novels');
        const novels = response.data.data;

        const select = document.getElementById('novelSelect');
        select.innerHTML = '<option value="">请选择小说...</option>';

        novels.forEach(novel => {
            const option = document.createElement('option');
            option.value = novel.id;
            option.textContent = `${novel.title} - ${novel.author} (${novel.category})`;
            if (novel.id === 'novel_001') {
                option.selected = true;
            }
            select.appendChild(option);
        });

        // 如果有默认选择，自动加载数据
        if (select.value) {
            loadDashboard();
        }

    } catch (error) {
        showError('加载小说列表失败: ' + (error.response?.data?.message || error.message));
    }
}

// 加载数据面板
async function loadDashboard() {
    showLoading();
    
    try {
        currentNovelId = document.getElementById('novelSelect').value;
        const days = document.getElementById('daysSelect').value;
        
        // 获取综合分析数据
        const response = await axios.get(`/api/analysis/comprehensive/${currentNovelId}?days=${days}`);
        currentData = response.data.data;

        // 更新关键指标 - 使用最新记录数据
        const latestRecord = currentData.latestRecord ||
                           (currentData.recentData && currentData.recentData.length > 0 ?
                            currentData.recentData[currentData.recentData.length - 1] : null);
        updateMetrics(latestRecord, currentData.summary);
        
        // 更新趋势分析
        updateTrendInfo(currentData.trendAnalysis, currentData.summary);
        
        // 更新图表
        updateChart(currentData.recentData);
        updateTrafficChart(currentData.recentData);
        updateCompletionChart(currentData.recentData);

        // 更新详细数据表格
        updateDataTable(currentData.recentData);
        
        hideLoading();
        showSuccess('数据加载完成');
        
    } catch (error) {
        hideLoading();
        showError('数据加载失败: ' + (error.response?.data?.message || error.message));
    }
}

// 更新关键指标
function updateMetrics(latestRecord, summary) {
    // 如果没有最新记录，显示默认值
    if (!latestRecord) {
        document.getElementById('totalReads').textContent = '-';
        document.getElementById('activeReaders').textContent = '-';
        document.getElementById('avgRating').textContent = '-';
        document.getElementById('totalComments').textContent = '-';
        document.getElementById('bookmarkCount').textContent = '-';
        document.getElementById('urgeUpdateCount').textContent = '-';
        document.getElementById('followCount').textContent = '-';
        document.getElementById('chapterCompletionRate').textContent = '-';
        document.getElementById('chapterFollowRate').textContent = '-';
        document.getElementById('wordCompletion10w').textContent = '-';
        document.getElementById('wordCompletion20w').textContent = '-';
        document.getElementById('wordCompletion30w').textContent = '-';
        document.getElementById('wordCompletion50w').textContent = '-';
        document.getElementById('wordCompletion80w').textContent = '-';
        document.getElementById('wordCompletion100w').textContent = '-';
        document.getElementById('normalStatus').textContent = '-';
        document.getElementById('totalTraffic').textContent = '-';
        return;
    }

    // 计算最新记录的流量总计
    const trafficSources = latestRecord.trafficSources || {};
    const totalTraffic = (trafficSources.bookstore || 0) + (trafficSources.category || 0) +
                        (trafficSources.bookshelf || 0) + (trafficSources.continueReading || 0) +
                        (trafficSources.search || 0) + (trafficSources.other || 0);

    // 更新第一行指标 - 显示最新记录的实际数据
    document.getElementById('totalReads').textContent = (latestRecord.readCount || 0).toLocaleString();
    document.getElementById('activeReaders').textContent = (latestRecord.activeReaders || 0).toLocaleString();
    document.getElementById('avgRating').textContent = latestRecord.rating || '0.0';
    document.getElementById('totalComments').textContent = (latestRecord.commentCount || 0).toLocaleString();
    document.getElementById('bookmarkCount').textContent = (latestRecord.bookmarkCount || 0).toLocaleString();
    document.getElementById('urgeUpdateCount').textContent = (latestRecord.urgeUpdateCount || 0).toLocaleString();

    // 更新第二行指标 - 确保数值转换正确
    document.getElementById('followCount').textContent = (latestRecord.followCount || 0).toLocaleString();

    // 处理完成率数据 - 可能是字符串格式
    const chapterCompletionRate = parseFloat(latestRecord.chapterCompletionRate || 0);
    const chapterFollowRate = parseFloat(latestRecord.chapterFollowRate || 0);

    document.getElementById('chapterCompletionRate').textContent = (chapterCompletionRate * 100).toFixed(1) + '%';
    document.getElementById('chapterFollowRate').textContent = (chapterFollowRate * 100).toFixed(1) + '%';

    // 处理字数读完率分段数据
    const wordRates = latestRecord.wordCompletionRates || {};
    document.getElementById('wordCompletion10w').textContent = ((parseFloat(wordRates['10w'] || 0)) * 100).toFixed(1) + '%';
    document.getElementById('wordCompletion20w').textContent = ((parseFloat(wordRates['20w'] || 0)) * 100).toFixed(1) + '%';
    document.getElementById('wordCompletion30w').textContent = ((parseFloat(wordRates['30w'] || 0)) * 100).toFixed(1) + '%';
    document.getElementById('wordCompletion50w').textContent = ((parseFloat(wordRates['50w'] || 0)) * 100).toFixed(1) + '%';
    document.getElementById('wordCompletion80w').textContent = ((parseFloat(wordRates['80w'] || 0)) * 100).toFixed(1) + '%';
    document.getElementById('wordCompletion100w').textContent = ((parseFloat(wordRates['100w'] || 0)) * 100).toFixed(1) + '%';
    document.getElementById('normalStatus').textContent = (summary?.normalStatusDays || 0) + '天';
    document.getElementById('totalTraffic').textContent = totalTraffic.toLocaleString();

    // 更新最新数据日期显示
    const dateElement = document.getElementById('latestDataDate');
    if (dateElement) {
        dateElement.textContent = `最新数据: ${latestRecord.date}`;
    }
}

// 更新趋势信息
function updateTrendInfo(trendAnalysis, summary) {
    const trendInfoDiv = document.getElementById('trendInfo');

    if (!trendAnalysis || trendAnalysis.error) {
        trendInfoDiv.innerHTML = '<p class="text-muted">暂无趋势分析数据</p>';
        return;
    }

    const trendIcon = trendAnalysis.trend === 'upward' ? '📈' :
                     trendAnalysis.trend === 'downward' ? '📉' : '📊';
    const trendText = trendAnalysis.trend === 'upward' ? '上升趋势' :
                     trendAnalysis.trend === 'downward' ? '下降趋势' : '稳定趋势';
    const trendColor = trendAnalysis.trend === 'upward' ? 'text-success' :
                      trendAnalysis.trend === 'downward' ? 'text-danger' : 'text-info';

    // 生成数据洞察
    let insights = [];

    if (summary) {
        // 评分分析
        const avgRating = parseFloat(summary.avgRating || 0);
        if (avgRating >= 8.5) {
            insights.push('作品评分优秀，用户满意度高');
        } else if (avgRating < 7.0) {
            insights.push('作品评分偏低，需关注内容质量');
        }

        // 完成率分析
        const chapterCompletion = summary.avgChapterCompletionRate * 100;
        if (chapterCompletion >= 80) {
            insights.push('章节读完率良好，内容吸引力强');
        } else if (chapterCompletion < 60) {
            insights.push('章节读完率偏低，建议优化内容');
        }

        // 互动分析
        const avgComments = summary.totalComments / summary.dataPoints;
        if (avgComments >= 20) {
            insights.push('用户互动活跃，社区氛围良好');
        } else if (avgComments < 5) {
            insights.push('用户互动较少，建议增强互动');
        }

        // 状态分析
        const normalRate = (summary.normalStatusDays / summary.dataPoints) * 100;
        if (normalRate >= 90) {
            insights.push('运营状态稳定');
        } else if (normalRate < 70) {
            insights.push('运营状态不稳定，需要关注');
        }
    }

    trendInfoDiv.innerHTML = `
        <div class="mb-3">
            <h6 class="${trendColor}">${trendIcon} ${trendText}</h6>
            <small class="text-muted">趋势强度: ${(trendAnalysis.trendStrength * 100).toFixed(1)}%</small>
        </div>

        <div class="mb-2">
            <strong>工作日平均:</strong> ${Math.round(trendAnalysis.weekdayAvg)}
        </div>
        <div class="mb-2">
            <strong>周末平均:</strong> ${Math.round(trendAnalysis.weekendAvg)}
        </div>
        <div class="mb-2">
            <strong>周末提升:</strong>
            <span class="${trendAnalysis.weekendBoost > 0 ? 'text-success' : 'text-danger'}">
                ${(trendAnalysis.weekendBoost * 100).toFixed(1)}%
            </span>
        </div>
        <div class="mb-2">
            <strong>数据波动:</strong> ${(trendAnalysis.volatility * 100).toFixed(1)}%
        </div>

        ${insights.length > 0 ? `
        <div class="mt-3">
            <h6><i class="fas fa-lightbulb"></i> 数据洞察</h6>
            ${insights.map(insight => `<p class="small text-info mb-1"><i class="fas fa-arrow-right"></i> ${insight}</p>`).join('')}
        </div>
        ` : ''}
    `;
}

// 更新图表
function updateChart(data) {
    const ctx = document.getElementById('readChart').getContext('2d');
    
    // 销毁现有图表
    if (window.readChart && typeof window.readChart.destroy === 'function') {
        window.readChart.destroy();
    }
    
    const labels = data.map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
    });
    
    const readCounts = data.map(item => item.readCount);
    
    window.readChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '阅读量',
                data: readCounts,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `阅读量: ${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            }
        }
    });
}

// 生成AI预测
async function generatePrediction() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 预测中...';
    button.disabled = true;
    
    try {
        const predictDays = document.getElementById('predictDays').value;
        
        const response = await axios.post(`/api/prediction/predict/${currentNovelId}`, {
            days: parseInt(predictDays),
            includeAnalysis: true
        });
        
        const predictionData = response.data.data;
        displayPredictionResult(predictionData);
        showSuccess('AI预测生成完成');
        
    } catch (error) {
        showError('预测生成失败: ' + (error.response?.data?.message || error.message));
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// 显示预测结果
function displayPredictionResult(data) {
    const resultDiv = document.getElementById('predictionResult');
    
    let predictionsHTML = '';
    if (data.predictions && data.predictions.length > 0) {
        predictionsHTML = `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>预测阅读量</th>
                            <th>置信度</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.predictions.map((pred, index) => {
                            const date = new Date();
                            date.setDate(date.getDate() + index + 1);
                            return `
                                <tr>
                                    <td>${date.getMonth() + 1}/${date.getDate()}</td>
                                    <td>${pred.readCount ? pred.readCount.toLocaleString() : 'N/A'}</td>
                                    <td>${pred.confidence ? (pred.confidence * 100).toFixed(1) + '%' : 'N/A'}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }
    
    resultDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-chart-line"></i> 预测数据</h6>
                ${predictionsHTML || '<p class="text-muted">暂无具体预测数据</p>'}
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-brain"></i> AI分析</h6>
                <div class="bg-light p-3 rounded">
                    <p><strong>置信度:</strong> ${(data.analysis.confidenceLevel * 100).toFixed(1)}%</p>
                    <p><strong>趋势分析:</strong></p>
                    <div style="max-height: 200px; overflow-y: auto;">
                        ${data.analysis.trendAnalysis.replace(/\n/g, '<br>')}
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-3">
            <h6><i class="fas fa-lightbulb"></i> 运营建议</h6>
            <div class="bg-info bg-opacity-10 p-3 rounded">
                ${data.analysis.recommendations.replace(/\n/g, '<br>')}
            </div>
        </div>
    `;
}

// 生成运营策略
async function generateStrategy() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中...';
    button.disabled = true;
    
    try {
        const response = await axios.post(`/api/prediction/strategy/${currentNovelId}`);
        const strategyData = response.data.data;
        
        document.getElementById('strategyResult').innerHTML = `
            <div class="bg-light p-4 rounded">
                <div style="white-space: pre-wrap; line-height: 1.6;">
                    ${strategyData.recommendations.replace(/\n/g, '<br>')}
                </div>
            </div>
            <div class="mt-3 text-muted small">
                <i class="fas fa-info-circle"></i> 
                基于 ${strategyData.basedOn.historicalDays} 天历史数据生成
                ${strategyData.basedOn.hasPredictions ? '，包含预测数据分析' : ''}
            </div>
        `;
        
        showSuccess('运营策略生成完成');
        
    } catch (error) {
        showError('策略生成失败: ' + (error.response?.data?.message || error.message));
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// 导出Excel报表
function exportExcel() {
    const days = document.getElementById('daysSelect').value;
    const url = `/api/report/excel/${currentNovelId}?days=${days}&includePredict=true`;
    window.open(url, '_blank');
    showSuccess('Excel报表导出中...');
}

// 导出HTML报表
function exportHTML() {
    const days = document.getElementById('daysSelect').value;
    const url = `/api/report/html/${currentNovelId}?days=${days}`;
    window.open(url, '_blank');
    showSuccess('HTML报表已在新窗口打开');
}

// 导出JSON数据
async function exportJSON() {
    try {
        const days = document.getElementById('daysSelect').value;
        const response = await axios.get(`/api/report/data/${currentNovelId}?days=${days}`);
        
        const dataStr = JSON.stringify(response.data.data, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `小说数据_${currentNovelId}_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        showSuccess('JSON数据导出完成');
        
    } catch (error) {
        showError('JSON导出失败: ' + (error.response?.data?.message || error.message));
    }
}

// 更新流量构成图表
function updateTrafficChart(data) {
    const ctx = document.getElementById('trafficChart').getContext('2d');

    // 销毁现有图表
    if (window.trafficChart && typeof window.trafficChart.destroy === 'function') {
        window.trafficChart.destroy();
    }

    // 使用最新记录的流量数据
    const latestRecord = data.length > 0 ? data[data.length - 1] : null;
    const trafficSources = latestRecord?.trafficSources || {};

    const trafficData = {
        bookstore: trafficSources.bookstore || 0,
        category: trafficSources.category || 0,
        bookshelf: trafficSources.bookshelf || 0,
        continueReading: trafficSources.continueReading || 0,
        search: trafficSources.search || 0,
        other: trafficSources.other || 0
    };

    window.trafficChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['书城', '分类', '书架', '继续阅读', '搜索', '其他'],
            datasets: [{
                data: [
                    trafficData.bookstore,
                    trafficData.category,
                    trafficData.bookshelf,
                    trafficData.continueReading,
                    trafficData.search,
                    trafficData.other
                ],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// 更新详细数据表格
function updateDataTable(data) {
    const tbody = document.getElementById('dataTableBody');

    if (!data || data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="18" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = '';

    data.forEach(item => {
        const row = document.createElement('tr');

        // 计算流量构成总计
        const trafficSources = item.trafficSources || {};
        const totalTraffic = (trafficSources.bookstore || 0) + (trafficSources.category || 0) +
                           (trafficSources.bookshelf || 0) + (trafficSources.continueReading || 0) +
                           (trafficSources.search || 0) + (trafficSources.other || 0);

        // 构建流量构成显示文本
        const trafficText = `总计:${totalTraffic} (书城:${trafficSources.bookstore || 0}, 分类:${trafficSources.category || 0}, 书架:${trafficSources.bookshelf || 0}, 继续:${trafficSources.continueReading || 0}, 搜索:${trafficSources.search || 0}, 其他:${trafficSources.other || 0})`;

        row.innerHTML = `
            <td>${item.date}</td>
            <td><span class="badge ${item.status === 'normal' ? 'bg-success' : 'bg-warning'}">${item.status === 'normal' ? '正常' : '异常'}</span></td>
            <td>${(item.readCount || 0).toLocaleString()}</td>
            <td>${(item.activeReaders || 0).toLocaleString()}</td>
            <td>${item.rating || '0.0'}</td>
            <td>${(item.commentCount || 0).toLocaleString()}</td>
            <td>${(item.bookmarkCount || 0).toLocaleString()}</td>
            <td>${(item.urgeUpdateCount || 0).toLocaleString()}</td>
            <td>${(item.followCount || 0).toLocaleString()}</td>
            <td>${((item.chapterCompletionRate || 0) * 100).toFixed(1)}%</td>
            <td>${((item.chapterFollowRate || 0) * 100).toFixed(1)}%</td>
            <td>${((item.wordCompletionRates?.['10w'] || 0) * 100).toFixed(1)}%</td>
            <td>${((item.wordCompletionRates?.['20w'] || 0) * 100).toFixed(1)}%</td>
            <td>${((item.wordCompletionRates?.['30w'] || 0) * 100).toFixed(1)}%</td>
            <td>${((item.wordCompletionRates?.['50w'] || 0) * 100).toFixed(1)}%</td>
            <td>${((item.wordCompletionRates?.['80w'] || 0) * 100).toFixed(1)}%</td>
            <td>${((item.wordCompletionRates?.['100w'] || 0) * 100).toFixed(1)}%</td>
            <td><small title="${trafficText}">${totalTraffic.toLocaleString()}</small></td>
        `;

        tbody.appendChild(row);
    });
}

// 更新完成率趋势图表
function updateCompletionChart(data) {
    const ctx = document.getElementById('completionChart').getContext('2d');

    // 销毁现有图表
    if (window.completionChart && typeof window.completionChart.destroy === 'function') {
        window.completionChart.destroy();
    }

    const labels = data.map(item => {
        const date = new Date(item.date);
        return `${date.getMonth() + 1}/${date.getDate()}`;
    });

    // 准备所有完成率数据
    const chapterCompletionRates = data.map(item => (item.chapterCompletionRate || 0) * 100);
    const chapterFollowRates = data.map(item => (item.chapterFollowRate || 0) * 100);

    // 字数读完率分段数据
    const word10wRates = data.map(item => {
        const wordRates = item.wordCompletionRates || {};
        return (parseFloat(wordRates['10w'] || 0)) * 100;
    });
    const word20wRates = data.map(item => {
        const wordRates = item.wordCompletionRates || {};
        return (parseFloat(wordRates['20w'] || 0)) * 100;
    });
    const word30wRates = data.map(item => {
        const wordRates = item.wordCompletionRates || {};
        return (parseFloat(wordRates['30w'] || 0)) * 100;
    });
    const word50wRates = data.map(item => {
        const wordRates = item.wordCompletionRates || {};
        return (parseFloat(wordRates['50w'] || 0)) * 100;
    });
    const word80wRates = data.map(item => {
        const wordRates = item.wordCompletionRates || {};
        return (parseFloat(wordRates['80w'] || 0)) * 100;
    });
    const word100wRates = data.map(item => {
        const wordRates = item.wordCompletionRates || {};
        return (parseFloat(wordRates['100w'] || 0)) * 100;
    });

    window.completionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '章节读完率',
                    data: chapterCompletionRates,
                    borderColor: '#FF6384',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderWidth: 2,
                    pointRadius: 4,
                    tension: 0.3,
                    fill: false
                },
                {
                    label: '章节跟读率',
                    data: chapterFollowRates,
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    borderWidth: 2,
                    pointRadius: 4,
                    tension: 0.3,
                    fill: false
                },
                {
                    label: '10万字读完率',
                    data: word10wRates,
                    borderColor: '#4BC0C0',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    tension: 0.3,
                    fill: false
                },
                {
                    label: '20万字读完率',
                    data: word20wRates,
                    borderColor: '#9966FF',
                    backgroundColor: 'rgba(153, 102, 255, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    tension: 0.3,
                    fill: false
                },
                {
                    label: '30万字读完率',
                    data: word30wRates,
                    borderColor: '#FF9F40',
                    backgroundColor: 'rgba(255, 159, 64, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    tension: 0.3,
                    fill: false
                },
                {
                    label: '50万字读完率',
                    data: word50wRates,
                    borderColor: '#FFCE56',
                    backgroundColor: 'rgba(255, 206, 86, 0.1)',
                    borderWidth: 3,
                    pointRadius: 4,
                    tension: 0.3,
                    fill: false
                },
                {
                    label: '80万字读完率',
                    data: word80wRates,
                    borderColor: '#FF6384',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    tension: 0.3,
                    fill: false,
                    borderDash: [5, 5]
                },
                {
                    label: '100万字读完率',
                    data: word100wRates,
                    borderColor: '#C9CBCF',
                    backgroundColor: 'rgba(201, 203, 207, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    tension: 0.3,
                    fill: false,
                    borderDash: [5, 5]
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    beginAtZero: true,
                    max: 100,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        },
                        stepSize: 10
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        font: {
                            size: 11
                        },
                        boxWidth: 12
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    callbacks: {
                        title: function(context) {
                            return `日期: ${context[0].label}`;
                        },
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
                        }
                    }
                }
            }
        }
    });

    // 添加完成率综合趋势分析
    updateCompletionAnalysis({
        chapterCompletion: chapterCompletionRates,
        chapterFollow: chapterFollowRates,
        word10w: word10wRates,
        word20w: word20wRates,
        word30w: word30wRates,
        word50w: word50wRates,
        word80w: word80wRates,
        word100w: word100wRates
    }, data);
}

// 完成率综合趋势分析
function updateCompletionAnalysis(allRates, data) {
    const analysisDiv = document.getElementById('wordCompletionAnalysis');

    if (!allRates || !data || data.length === 0) {
        analysisDiv.innerHTML = '<small class="text-muted">暂无数据分析</small>';
        return;
    }

    // 计算各指标的最新值
    const latestValues = {
        chapterCompletion: allRates.chapterCompletion[allRates.chapterCompletion.length - 1] || 0,
        chapterFollow: allRates.chapterFollow[allRates.chapterFollow.length - 1] || 0,
        word50w: allRates.word50w[allRates.word50w.length - 1] || 0
    };

    // 计算50万字读完率趋势（作为主要指标）
    const word50wRates = allRates.word50w;
    const avgRate = word50wRates.reduce((sum, rate) => sum + rate, 0) / word50wRates.length;
    const firstRate = word50wRates[0];
    const latestRate = word50wRates[word50wRates.length - 1];
    const trendChange = latestRate - firstRate;

    // 趋势判断
    let trendIcon, trendText, trendColor;
    if (trendChange > 2) {
        trendIcon = '📈';
        trendText = '上升趋势';
        trendColor = 'text-success';
    } else if (trendChange < -2) {
        trendIcon = '📉';
        trendText = '下降趋势';
        trendColor = 'text-danger';
    } else {
        trendIcon = '📊';
        trendText = '稳定趋势';
        trendColor = 'text-info';
    }

    // 找出表现最好和最差的指标
    const currentRates = [
        { name: '章节读完率', value: latestValues.chapterCompletion },
        { name: '章节跟读率', value: latestValues.chapterFollow },
        { name: '10万字读完率', value: allRates.word10w[allRates.word10w.length - 1] || 0 },
        { name: '20万字读完率', value: allRates.word20w[allRates.word20w.length - 1] || 0 },
        { name: '30万字读完率', value: allRates.word30w[allRates.word30w.length - 1] || 0 },
        { name: '50万字读完率', value: allRates.word50w[allRates.word50w.length - 1] || 0 },
        { name: '80万字读完率', value: allRates.word80w[allRates.word80w.length - 1] || 0 },
        { name: '100万字读完率', value: allRates.word100w[allRates.word100w.length - 1] || 0 }
    ];

    const bestRate = currentRates.reduce((max, current) => current.value > max.value ? current : max);
    const worstRate = currentRates.reduce((min, current) => current.value < min.value ? current : min);

    // 字数读完率递减合理性检查
    const wordRates = [
        allRates.word10w[allRates.word10w.length - 1] || 0,
        allRates.word20w[allRates.word20w.length - 1] || 0,
        allRates.word30w[allRates.word30w.length - 1] || 0,
        allRates.word50w[allRates.word50w.length - 1] || 0,
        allRates.word80w[allRates.word80w.length - 1] || 0,
        allRates.word100w[allRates.word100w.length - 1] || 0
    ];

    let isReasonableDecline = true;
    for (let i = 1; i < wordRates.length; i++) {
        if (wordRates[i] > wordRates[i-1]) {
            isReasonableDecline = false;
            break;
        }
    }

    analysisDiv.innerHTML = `
        <div class="row">
            <div class="col-md-4">
                <h6><i class="fas fa-chart-line"></i> 主要趋势</h6>
                <p class="mb-1"><span class="${trendColor}">${trendIcon} ${trendText}</span></p>
                <small class="text-muted">
                    50万字读完率: ${trendChange > 0 ? '+' : ''}${trendChange.toFixed(1)}%
                </small>
            </div>
            <div class="col-md-4">
                <h6><i class="fas fa-star"></i> 表现对比</h6>
                <p class="mb-1">
                    <span class="text-success">最佳: ${bestRate.name}</span><br>
                    <small class="text-muted">${bestRate.value.toFixed(1)}%</small>
                </p>
            </div>
            <div class="col-md-4">
                <h6><i class="fas fa-check-circle"></i> 数据质量</h6>
                <p class="mb-1">
                    <span class="${isReasonableDecline ? 'text-success' : 'text-warning'}">
                        ${isReasonableDecline ? '✓ 递减合理' : '⚠ 数据异常'}
                    </span>
                </p>
                <small class="text-muted">字数读完率分布</small>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    当前值: 章节读完率 ${latestValues.chapterCompletion.toFixed(1)}% |
                    章节跟读率 ${latestValues.chapterFollow.toFixed(1)}% |
                    50万字读完率 ${latestValues.word50w.toFixed(1)}%
                </small>
            </div>
        </div>
    `;
}

// 刷新数据
function refreshData() {
    if (!currentNovelId) {
        showError('请先选择小说');
        return;
    }

    loadDashboard();
    showSuccess('数据已刷新');
}
