const moment = require('moment');
const _ = require('lodash');

class DataService {
    constructor() {
        // 模拟数据存储，实际项目中应该连接数据库
        this.data = {
            novels: new Map(),
            dailyStats: new Map(),
            userProfiles: new Map()
        };
        
        // 初始化一些示例数据
        this.initSampleData();
    }

    /**
     * 初始化示例数据
     */
    initSampleData() {
        // 添加多个示例小说
        const sampleNovels = [
            {
                id: 'novel_001',
                title: '都市霸主',
                author: '笔名作者',
                category: '都市言情',
                publishDate: '2024-01-01',
                status: 'ongoing'
            },
            {
                id: 'novel_002',
                title: '修仙传说',
                author: '仙侠大师',
                category: '仙侠修真',
                publishDate: '2024-01-15',
                status: 'ongoing'
            },
            {
                id: 'novel_003',
                title: '科幻未来',
                author: '科幻作家',
                category: '科幻小说',
                publishDate: '2024-02-01',
                status: 'completed'
            }
        ];

        // 保存小说信息
        sampleNovels.forEach(novel => {
            this.data.novels.set(novel.id, novel);
        });

        // 为每个小说生成30天的示例数据
        sampleNovels.forEach(novel => {
            for (let i = 30; i >= 0; i--) {
                const date = moment().subtract(i, 'days').format('YYYY-MM-DD');
                const stats = this.generateSampleDailyStats(date, i, novel.id);
                this.data.dailyStats.set(`${novel.id}_${date}`, stats);
            }
        });
    }

    /**
     * 生成示例日常统计数据
     */
    generateSampleDailyStats(date, daysAgo, novelId = 'novel_001') {
        const baseReads = 1000;
        const trend = Math.sin(daysAgo * 0.1) * 200; // 模拟波动
        const weekendBonus = moment(date).day() === 0 || moment(date).day() === 6 ? 300 : 0;

        // 新的数据格式
        return {
            novelId: novelId,
            date: date,
            status: Math.random() > 0.1 ? 'normal' : 'abnormal', // 状态
            readCount: Math.max(100, Math.floor(baseReads + trend + weekendBonus + Math.random() * 200)), // 阅读人数
            activeReaders: Math.floor(Math.random() * 300 + 200), // 在读人数
            rating: (Math.random() * 2 + 8).toFixed(1), // 作品评分 8.0-10.0
            commentCount: Math.floor(Math.random() * 30 + 10), // 评论次数
            bookmarkCount: Math.floor(Math.random() * 80 + 40), // 加书架人数
            urgeUpdateCount: Math.floor(Math.random() * 25 + 10), // 催更人数
            followCount: Math.floor(Math.random() * 60 + 30), // 追更人数
            chapterCompletionRate: (Math.random() * 0.3 + 0.6).toFixed(3), // 章节读完率 60%-90%
            chapterFollowRate: (Math.random() * 0.4 + 0.5).toFixed(3), // 章节跟读率 50%-90%
            wordCompletionRate: (Math.random() * 0.25 + 0.65).toFixed(3), // 字数读完率 65%-90%
            trafficSources: { // 流量构成
                bookstore: Math.floor(Math.random() * 30 + 20), // 书城
                category: Math.floor(Math.random() * 25 + 15), // 分类
                bookshelf: Math.floor(Math.random() * 20 + 25), // 书架
                continueReading: Math.floor(Math.random() * 15 + 20), // 继续阅读
                search: Math.floor(Math.random() * 10 + 5), // 搜索
                other: Math.floor(Math.random() * 10 + 5) // 其他
            },
            // 保留原有字段以兼容现有功能
            subscribeCount: Math.floor(Math.random() * 50 + 20),
            likeCount: Math.floor(Math.random() * 100 + 50),
            chapterViews: Math.floor(Math.random() * 500 + 200),
            avgReadTime: Math.floor(Math.random() * 300 + 180), // 秒
            bounceRate: Math.random() * 0.3 + 0.1, // 10%-40%
            conversionRate: Math.random() * 0.05 + 0.01 // 1%-6%
        };
    }

    /**
     * 获取小说基本信息
     */
    getNovelInfo(novelId) {
        return this.data.novels.get(novelId);
    }

    /**
     * 获取历史数据
     */
    getHistoricalData(novelId, days = 30) {
        const endDate = moment();
        const startDate = moment().subtract(days, 'days');
        const historicalData = [];

        for (let date = startDate.clone(); date.isSameOrBefore(endDate); date.add(1, 'day')) {
            const dateStr = date.format('YYYY-MM-DD');
            const key = `${novelId}_${dateStr}`;
            const stats = this.data.dailyStats.get(key);
            
            if (stats) {
                historicalData.push(stats);
            }
        }

        return historicalData.sort((a, b) => moment(a.date).diff(moment(b.date)));
    }

    /**
     * 获取数据统计摘要
     */
    getDataSummary(novelId, days = 7) {
        const data = this.getHistoricalData(novelId, days);
        
        if (data.length === 0) {
            return null;
        }

        const summary = {
            totalReads: _.sumBy(data, 'readCount'),
            totalSubscribes: _.sumBy(data, 'subscribeCount'),
            totalComments: _.sumBy(data, 'commentCount'),
            totalLikes: _.sumBy(data, 'likeCount'),
            totalFollows: _.sumBy(data, 'followCount'),
            avgReadTime: _.meanBy(data, 'avgReadTime'),
            avgBounceRate: _.meanBy(data, 'bounceRate'),
            avgConversionRate: _.meanBy(data, 'conversionRate'),
            dataPoints: data.length
        };

        // 计算增长率
        if (data.length >= 2) {
            const latest = data[data.length - 1];
            const previous = data[data.length - 2];
            
            summary.growthRates = {
                reads: this.calculateGrowthRate(previous.readCount, latest.readCount),
                subscribes: this.calculateGrowthRate(previous.subscribeCount, latest.subscribeCount),
                comments: this.calculateGrowthRate(previous.commentCount, latest.commentCount),
                likes: this.calculateGrowthRate(previous.likeCount, latest.likeCount)
            };
        }

        return summary;
    }

    /**
     * 计算增长率
     */
    calculateGrowthRate(previous, current) {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous * 100).toFixed(2);
    }

    /**
     * 获取趋势分析
     */
    getTrendAnalysis(novelId, days = 30) {
        const data = this.getHistoricalData(novelId, days);
        
        if (data.length < 7) {
            return { error: '数据不足，无法进行趋势分析' };
        }

        const readCounts = data.map(d => d.readCount);
        const dates = data.map(d => d.date);

        // 简单的趋势计算
        const firstWeek = readCounts.slice(0, 7);
        const lastWeek = readCounts.slice(-7);
        
        const firstWeekAvg = _.mean(firstWeek);
        const lastWeekAvg = _.mean(lastWeek);
        
        const trend = lastWeekAvg > firstWeekAvg ? 'upward' : 
                     lastWeekAvg < firstWeekAvg ? 'downward' : 'stable';

        // 检测周期性模式
        const weekdayReads = [];
        const weekendReads = [];
        
        data.forEach(item => {
            const dayOfWeek = moment(item.date).day();
            if (dayOfWeek === 0 || dayOfWeek === 6) {
                weekendReads.push(item.readCount);
            } else {
                weekdayReads.push(item.readCount);
            }
        });

        return {
            trend: trend,
            trendStrength: Math.abs(lastWeekAvg - firstWeekAvg) / firstWeekAvg,
            weekdayAvg: _.mean(weekdayReads),
            weekendAvg: _.mean(weekendReads),
            weekendBoost: (_.mean(weekendReads) - _.mean(weekdayReads)) / _.mean(weekdayReads),
            volatility: this.calculateVolatility(readCounts),
            peakDays: this.findPeakDays(data),
            lowDays: this.findLowDays(data)
        };
    }

    /**
     * 计算波动率
     */
    calculateVolatility(values) {
        const mean = _.mean(values);
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
        const variance = _.mean(squaredDiffs);
        return Math.sqrt(variance) / mean;
    }

    /**
     * 找出高峰日期
     */
    findPeakDays(data) {
        const sorted = _.orderBy(data, 'readCount', 'desc');
        return sorted.slice(0, 3).map(item => ({
            date: item.date,
            readCount: item.readCount,
            dayOfWeek: moment(item.date).format('dddd')
        }));
    }

    /**
     * 找出低谷日期
     */
    findLowDays(data) {
        const sorted = _.orderBy(data, 'readCount', 'asc');
        return sorted.slice(0, 3).map(item => ({
            date: item.date,
            readCount: item.readCount,
            dayOfWeek: moment(item.date).format('dddd')
        }));
    }

    /**
     * 保存预测数据
     */
    savePredictionData(novelId, predictions) {
        const key = `predictions_${novelId}_${moment().format('YYYY-MM-DD')}`;
        this.data.predictions = this.data.predictions || new Map();
        this.data.predictions.set(key, {
            novelId,
            predictions,
            createdAt: new Date(),
            validUntil: moment().add(7, 'days').toDate()
        });
    }

    /**
     * 获取预测数据
     */
    getPredictionData(novelId) {
        const key = `predictions_${novelId}_${moment().format('YYYY-MM-DD')}`;
        this.data.predictions = this.data.predictions || new Map();
        return this.data.predictions.get(key);
    }

    /**
     * 获取所有小说列表
     */
    getAllNovels() {
        return Array.from(this.data.novels.values());
    }

    /**
     * 添加新小说
     */
    addNovel(novelData) {
        const novel = {
            id: novelData.id || `novel_${Date.now()}`,
            title: novelData.title,
            author: novelData.author,
            category: novelData.category,
            publishDate: novelData.publishDate || moment().format('YYYY-MM-DD'),
            status: novelData.status || 'ongoing'
        };

        this.data.novels.set(novel.id, novel);
        return novel;
    }

    /**
     * 更新小说信息
     */
    updateNovel(novelId, updateData) {
        const novel = this.data.novels.get(novelId);
        if (!novel) {
            throw new Error('小说不存在');
        }

        const updatedNovel = { ...novel, ...updateData };
        this.data.novels.set(novelId, updatedNovel);
        return updatedNovel;
    }

    /**
     * 删除小说
     */
    deleteNovel(novelId) {
        const deleted = this.data.novels.delete(novelId);

        // 删除相关的统计数据
        const keysToDelete = [];
        for (const key of this.data.dailyStats.keys()) {
            if (key.startsWith(`${novelId}_`)) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => this.data.dailyStats.delete(key));

        return deleted;
    }

    /**
     * 记录单日数据
     */
    recordDailyData(novelId, date, dataRecord) {
        const key = `${novelId}_${date}`;

        // 验证数据格式
        const requiredFields = [
            'status', 'readCount', 'activeReaders', 'rating', 'commentCount',
            'bookmarkCount', 'urgeUpdateCount', 'followCount', 'chapterCompletionRate',
            'chapterFollowRate', 'wordCompletionRate', 'trafficSources'
        ];

        for (const field of requiredFields) {
            if (!(field in dataRecord)) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }

        // 验证流量构成数据
        const requiredTrafficFields = ['bookstore', 'category', 'bookshelf', 'continueReading', 'search', 'other'];
        for (const field of requiredTrafficFields) {
            if (!(field in dataRecord.trafficSources)) {
                throw new Error(`缺少流量构成字段: ${field}`);
            }
        }

        const record = {
            novelId,
            date,
            ...dataRecord,
            recordedAt: new Date()
        };

        this.data.dailyStats.set(key, record);
        return record;
    }

    /**
     * 批量记录数据
     */
    recordBatchData(novelId, dataRecords) {
        const results = [];
        const errors = [];

        for (const record of dataRecords) {
            try {
                const result = this.recordDailyData(novelId, record.date, record);
                results.push(result);
            } catch (error) {
                errors.push({
                    date: record.date,
                    error: error.message
                });
            }
        }

        return { results, errors };
    }

    /**
     * 获取数据记录模板
     */
    getDataTemplate() {
        return {
            date: moment().format('YYYY-MM-DD'),
            status: 'normal', // normal, abnormal
            readCount: 0, // 阅读人数
            activeReaders: 0, // 在读人数
            rating: '0.0', // 作品评分
            commentCount: 0, // 评论次数
            bookmarkCount: 0, // 加书架人数
            urgeUpdateCount: 0, // 催更人数
            followCount: 0, // 追更人数
            chapterCompletionRate: '0.000', // 章节读完率
            chapterFollowRate: '0.000', // 章节跟读率
            wordCompletionRate: '0.000', // 字数读完率
            trafficSources: { // 流量构成
                bookstore: 0, // 书城
                category: 0, // 分类
                bookshelf: 0, // 书架
                continueReading: 0, // 继续阅读
                search: 0, // 搜索
                other: 0 // 其他
            }
        };
    }

    /**
     * 导出数据为CSV格式
     */
    exportToCSV(novelId, days = 30) {
        const data = this.getHistoricalData(novelId, days);

        if (data.length === 0) {
            return null;
        }

        // CSV头部
        const headers = [
            '日期', '状态', '阅读人数', '在读人数', '作品评分', '评论次数',
            '加书架人数', '催更人数', '追更人数', '章节读完率', '章节跟读率',
            '字数读完率', '书城流量', '分类流量', '书架流量', '继续阅读流量',
            '搜索流量', '其他流量'
        ];

        // CSV数据行
        const rows = data.map(item => [
            item.date,
            item.status || 'normal',
            item.readCount || 0,
            item.activeReaders || 0,
            item.rating || '0.0',
            item.commentCount || 0,
            item.bookmarkCount || 0,
            item.urgeUpdateCount || 0,
            item.followCount || 0,
            item.chapterCompletionRate || '0.000',
            item.chapterFollowRate || '0.000',
            item.wordCompletionRate || '0.000',
            item.trafficSources?.bookstore || 0,
            item.trafficSources?.category || 0,
            item.trafficSources?.bookshelf || 0,
            item.trafficSources?.continueReading || 0,
            item.trafficSources?.search || 0,
            item.trafficSources?.other || 0
        ]);

        // 组合CSV内容
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        return csvContent;
    }
}

module.exports = new DataService();
