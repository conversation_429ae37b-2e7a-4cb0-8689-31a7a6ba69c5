<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表导出 - 小说运营数据分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/common.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .format-card {
            background: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin: 0.5rem 0;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            border: 2px solid transparent;
        }
        .format-card:hover {
            background: var(--light-color);
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
            border-color: var(--primary-color);
        }
        .format-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .excel-icon { color: #217346; }
        .html-icon { color: #e34c26; }
        .csv-icon { color: #28a745; }
        .json-icon { color: #f39c12; }
        .pdf-icon { color: #dc3545; }
        .preview-section {
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin: 1rem 0;
            border: 1px solid var(--border-color);
        }
        .download-history {
            max-height: 300px;
            overflow-y: auto;
        }
        .text-purple {
            color: #6f42c1 !important;
        }
        .alert-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
        .table-sm th, .table-sm td {
            padding: 0.5rem;
            font-size: 0.875rem;
        }
        @media (max-width: 768px) {
            .format-card {
                margin: 0.75rem 0;
                padding: 1rem;
            }
            .format-icon {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-download"></i> 报表导出
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">首页</a>
                    <a class="nav-link" href="dashboard.html">数据面板</a>
                    <a class="nav-link" href="prediction.html">AI预测</a>
                    <a class="nav-link active" href="reports.html">报表导出</a>
                    <a class="nav-link" href="data-management.html">数据管理</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 报表导出标题 -->
        <div class="report-header text-center">
            <h2><i class="fas fa-file-export"></i> 报表导出中心</h2>
            <p class="mb-0">多格式数据报表导出，专业分析报告生成</p>
        </div>

        <!-- 小说选择和导出设置 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-book"></i> 选择小说</h5>
                        <div class="mb-3">
                            <label for="novelSelect" class="form-label">选择要导出的小说</label>
                            <select class="form-select" id="novelSelect">
                                <option value="">加载中...</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="dateRange" class="form-label">数据范围</label>
                            <select class="form-select" id="dateRange">
                                <option value="7">最近7天</option>
                                <option value="15">最近15天</option>
                                <option value="30" selected>最近30天</option>
                                <option value="60">最近60天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-cog"></i> 导出选项</h5>
                        <div class="mb-3">
                            <label class="form-label">包含内容</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                                <label class="form-check-label" for="includeCharts">图表分析</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includePrediction" checked>
                                <label class="form-check-label" for="includePrediction">AI预测数据</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeStrategy" checked>
                                <label class="form-check-label" for="includeStrategy">运营建议</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="reportTemplate" class="form-label">报表模板</label>
                            <select class="form-select" id="reportTemplate">
                                <option value="standard">标准报表</option>
                                <option value="detailed">详细报表</option>
                                <option value="summary">摘要报表</option>
                                <option value="executive">高管报表</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出格式选择 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card report-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-file-alt"></i> 选择导出格式</h5>
                        <div class="row">
                            <div class="col-md-2">
                                <div class="format-card" onclick="exportReport('excel')">
                                    <i class="fas fa-file-excel format-icon excel-icon"></i>
                                    <h6>Excel</h6>
                                    <p class="small text-muted">完整数据表格<br>图表分析</p>
                                    <button class="btn btn-success btn-sm">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="format-card" onclick="exportReport('html')">
                                    <i class="fas fa-file-code format-icon html-icon"></i>
                                    <h6>HTML</h6>
                                    <p class="small text-muted">网页报表<br>可视化展示</p>
                                    <button class="btn btn-danger btn-sm">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="format-card" onclick="exportReport('csv')">
                                    <i class="fas fa-file-csv format-icon csv-icon"></i>
                                    <h6>CSV</h6>
                                    <p class="small text-muted">纯数据格式<br>易于处理</p>
                                    <button class="btn btn-success btn-sm">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="format-card" onclick="exportReport('json')">
                                    <i class="fas fa-file-code format-icon json-icon"></i>
                                    <h6>JSON</h6>
                                    <p class="small text-muted">结构化数据<br>API友好</p>
                                    <button class="btn btn-warning btn-sm">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="format-card" onclick="exportReport('pdf')">
                                    <i class="fas fa-file-pdf format-icon pdf-icon"></i>
                                    <h6>PDF</h6>
                                    <p class="small text-muted">专业报告<br>打印友好</p>
                                    <button class="btn btn-danger btn-sm">
                                        <i class="fas fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="format-card" onclick="previewReport()">
                                    <i class="fas fa-eye format-icon text-primary"></i>
                                    <h6>预览</h6>
                                    <p class="small text-muted">在线预览<br>确认内容</p>
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> 预览
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 报表预览 -->
        <div class="row mb-4" id="previewSection" style="display: none;">
            <div class="col-12">
                <div class="card report-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-eye"></i> 报表预览</h5>
                        <div id="reportPreview" class="preview-section">
                            <p class="text-muted text-center">点击预览按钮查看报表内容</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下载历史 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card report-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-history"></i> 下载历史</h5>
                        <div class="download-history">
                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>时间</th>
                                            <th>小说</th>
                                            <th>格式</th>
                                            <th>范围</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="downloadHistoryBody">
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">暂无下载记录</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card report-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-info-circle"></i> 导出说明</h5>
                        <div class="small">
                            <p><strong>Excel:</strong> 包含完整数据表格和图表，适合详细分析</p>
                            <p><strong>HTML:</strong> 网页格式报表，可在浏览器中查看</p>
                            <p><strong>CSV:</strong> 纯数据格式，适合导入其他系统</p>
                            <p><strong>JSON:</strong> 结构化数据，适合程序处理</p>
                            <p><strong>PDF:</strong> 专业报告格式，适合打印和分享</p>
                        </div>
                        <hr>
                        <div class="text-center">
                            <button class="btn btn-outline-primary btn-sm" onclick="clearHistory()">
                                <i class="fas fa-trash"></i> 清空历史
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card report-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-bolt"></i> 快速操作</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <a href="dashboard.html" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-chart-line"></i> 查看数据面板
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="prediction.html" class="btn btn-outline-success w-100 mb-2">
                                    <i class="fas fa-robot"></i> AI预测分析
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="data-management.html" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="fas fa-database"></i> 管理数据
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-secondary w-100 mb-2" onclick="refreshData()">
                                    <i class="fas fa-sync"></i> 刷新数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/common.js"></script>
    <script src="js/reports.js"></script>
</body>
</html>
