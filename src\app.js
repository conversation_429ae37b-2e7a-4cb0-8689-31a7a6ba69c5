const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const dataAnalysisRoutes = require('./routes/dataAnalysis');
const predictionRoutes = require('./routes/prediction');
const strategyRoutes = require('./routes/strategy');
const reportRoutes = require('./routes/report');
const dataManagementRoutes = require('./routes/dataManagement');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// 路由
app.use('/api/analysis', dataAnalysisRoutes);
app.use('/api/prediction', predictionRoutes);
app.use('/api/strategy', strategyRoutes);
app.use('/api/report', reportRoutes);
app.use('/api/data', dataManagementRoutes);

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ 
        error: '服务器内部错误',
        message: err.message 
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ 
        error: '接口不存在' 
    });
});

app.listen(PORT, () => {
    console.log(`小说运营数据分析系统启动成功！`);
    console.log(`服务器运行在: http://localhost:${PORT}`);
    console.log(`API文档: http://localhost:${PORT}/api/docs`);
});

module.exports = app;
