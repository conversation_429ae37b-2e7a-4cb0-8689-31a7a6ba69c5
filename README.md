# 小说运营数据分析系统

一个基于AI的小说运营数据分析系统，配合豆包AI进行数据预测和运营策略分析。

## 功能特性

### 📊 数据监测与分析
- **日常数据跟踪**: 监控阅读量、订阅量、评论数、点赞数、追更人数等核心指标
- **深入数据分析**: 多维度数据分析，识别数据变化规律和原因
- **用户画像构建**: 基于用户行为数据构建读者画像

### 📝 数据记录与统计
- **标准化数据格式**: 支持日期、状态、阅读人数、在读人数、作品评分、评论次数、加书架人数、催更人数、追更人数、章节读完率、章节跟读率、字数读完率、流量构成等完整数据记录
- **流量构成分析**: 详细记录书城、分类、书架、继续阅读、搜索、其他等各渠道流量数据
- **数据统计汇总**: 自动计算平均值、趋势变化、流量分布等统计指标
- **小说管理**: 支持添加、编辑、删除小说，自定义切换分析对象

### 🤖 AI智能预测
- **数据预测**: 使用豆包AI预测未来3-14天的数据变化
- **趋势分析**: 识别上升、下降、稳定等趋势模式
- **置信度评估**: 提供预测结果的可信度评分

### 📈 运营策略优化
- **策略建议**: AI生成个性化运营策略建议
- **内容优化**: 基于数据反馈提供内容改进建议
- **推广优化**: 分析推广渠道效果，优化推广策略

### 📋 报表导出
- **Excel报表**: 生成详细的数据分析Excel文件
- **HTML报表**: 生成可视化的网页报表
- **CSV格式**: 导出标准CSV格式数据，支持Excel直接打开
- **JSON数据**: 导出原始数据用于进一步分析

### 🔍 竞品分析
- **数据对比**: 多个作品数据对比分析
- **市场洞察**: 了解行业趋势和竞争态势

## 技术架构

### 后端技术栈
- **Node.js + Express**: 服务器框架
- **豆包AI API**: 数据预测和分析
- **Moment.js**: 日期处理
- **Lodash**: 数据处理工具
- **XLSX**: Excel文件生成

### 前端技术栈
- **Bootstrap 5**: UI框架
- **Chart.js**: 数据可视化
- **Axios**: HTTP请求
- **Font Awesome**: 图标库

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
复制 `.env` 文件并配置豆包AI API密钥：
```env
DOUBAO_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DOUBAO_API_KEY=your_api_key_here
DOUBAO_MODEL=doubao-1-5-pro-32k-250115
PORT=3000
```

### 3. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 4. 访问系统
打开浏览器访问: http://localhost:3000

## API接口文档

### 数据分析接口

#### 获取小说信息
```
GET /api/analysis/novel/:novelId
```

#### 获取历史数据
```
GET /api/analysis/historical/:novelId?days=30
```

#### 获取数据摘要
```
GET /api/analysis/summary/:novelId?days=7
```

#### 获取趋势分析
```
GET /api/analysis/trend/:novelId?days=30
```

#### 综合数据分析
```
GET /api/analysis/comprehensive/:novelId?days=30
```

### 数据管理接口

#### 获取所有小说列表
```
GET /api/data/novels
```

#### 添加新小说
```
POST /api/data/novels
Body: {
  "title": "小说标题",
  "author": "作者名称",
  "category": "分类",
  "publishDate": "2024-01-01",
  "status": "ongoing"
}
```

#### 获取数据记录模板
```
GET /api/data/template
```

#### 记录单日数据
```
POST /api/data/record/:novelId
Body: {
  "date": "2024-01-01",
  "status": "normal",
  "readCount": 1000,
  "activeReaders": 500,
  "rating": "8.5",
  "commentCount": 50,
  "bookmarkCount": 200,
  "urgeUpdateCount": 30,
  "followCount": 150,
  "chapterCompletionRate": "0.850",
  "chapterFollowRate": "0.750",
  "wordCompletionRate": "0.800",
  "trafficSources": {
    "bookstore": 300,
    "category": 200,
    "bookshelf": 250,
    "continueReading": 150,
    "search": 80,
    "other": 20
  }
}
```

#### 批量记录数据
```
POST /api/data/batch-record/:novelId
Body: {
  "records": [数据记录数组]
}
```

#### 获取数据统计
```
GET /api/data/stats/:novelId?days=30
```

#### 导出CSV数据
```
GET /api/data/export/csv/:novelId?days=30
```



### AI预测接口

#### 生成数据预测
```
POST /api/prediction/predict/:novelId
Body: {
  "days": 7,
  "includeAnalysis": true
}
```

#### 获取运营策略建议
```
POST /api/prediction/strategy/:novelId
```

#### 批量预测
```
POST /api/prediction/batch-predict
Body: {
  "novelIds": ["novel_001", "novel_002"],
  "days": 7
}
```

### 报表导出接口

#### 导出Excel报表
```
GET /api/report/excel/:novelId?days=30&includePredict=true
```

#### 导出HTML报表
```
GET /api/report/html/:novelId?days=30
```

#### 获取报表数据
```
GET /api/report/data/:novelId?days=30
```

## 使用指南

### 1. 数据监测
- 选择要分析的小说
- 设置分析时间范围（7-60天）
- 查看关键指标和趋势图表

### 2. 数据记录与管理
- 访问 `/data-management.html` 进入数据管理页面
- 添加新小说或选择现有小说
- 使用标准化表单记录每日运营数据
- 查看数据统计和历史记录表格
- 导出CSV、JSON等格式的数据文件



### 3. AI预测
- 点击"生成预测"按钮
- 选择预测天数（3-14天）
- 查看预测结果和AI分析

### 4. 运营策略
- 点击"生成建议"按钮
- 获取AI生成的运营策略建议
- 根据建议优化运营方案

### 5. 报表导出
- 选择导出格式（Excel/HTML/CSV/JSON）
- 下载详细的数据分析报表
- 用于汇报和存档

## 数据结构

### 小说数据结构
```javascript
{
  id: "novel_001",
  title: "小说标题",
  author: "作者名称",
  category: "分类",
  publishDate: "2024-01-01",
  status: "ongoing"
}
```

### 标准化数据记录格式
```javascript
{
  novelId: "novel_001",
  date: "2024-01-01",
  status: "normal",                    // 状态 (normal/abnormal)
  readCount: 1000,                     // 阅读人数
  activeReaders: 500,                  // 在读人数
  rating: "8.5",                       // 作品评分
  commentCount: 30,                    // 评论次数
  bookmarkCount: 200,                  // 加书架人数
  urgeUpdateCount: 25,                 // 催更人数
  followCount: 150,                    // 追更人数
  chapterCompletionRate: "0.850",      // 章节读完率
  chapterFollowRate: "0.750",          // 章节跟读率
  wordCompletionRate: "0.800",         // 字数读完率
  trafficSources: {                    // 流量构成
    bookstore: 300,                    // 书城
    category: 200,                     // 分类
    bookshelf: 250,                    // 书架
    continueReading: 150,              // 继续阅读
    search: 80,                        // 搜索
    other: 20                          // 其他
  },
  // 兼容字段
  subscribeCount: 50,                  // 订阅量
  likeCount: 100,                      // 点赞数
  chapterViews: 500,                   // 章节浏览量
  avgReadTime: 300,                    // 平均阅读时长(秒)
  bounceRate: 0.2,                     // 跳出率
  conversionRate: 0.05                 // 转化率
}
```

## 部署说明

### 生产环境部署
1. 设置环境变量
2. 安装PM2进程管理器
3. 启动应用：`pm2 start src/app.js --name novel-analysis`
4. 配置Nginx反向代理
5. 设置SSL证书

### Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 注意事项

1. **API限制**: 豆包AI API有调用频率限制，建议合理控制请求频率
2. **数据存储**: 当前使用内存存储，生产环境建议使用数据库
3. **安全性**: 生产环境需要添加身份验证和权限控制
4. **监控**: 建议添加日志记录和性能监控

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发团队。
