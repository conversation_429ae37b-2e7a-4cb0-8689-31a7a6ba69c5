const express = require('express');
const router = express.Router();
const dataService = require('../services/dataService');
const aiService = require('../services/aiService');

/**
 * 获取小说基本信息
 */
router.get('/novel/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const novelInfo = dataService.getNovelInfo(novelId);
        
        if (!novelInfo) {
            return res.status(404).json({ error: '小说不存在' });
        }
        
        res.json({
            success: true,
            data: novelInfo
        });
    } catch (error) {
        res.status(500).json({ 
            error: '获取小说信息失败', 
            message: error.message 
        });
    }
});

/**
 * 获取历史数据
 */
router.get('/historical/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 30 } = req.query;
        
        const historicalData = dataService.getHistoricalData(novelId, parseInt(days));
        
        res.json({
            success: true,
            data: historicalData,
            meta: {
                novelId,
                days: parseInt(days),
                dataPoints: historicalData.length
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '获取历史数据失败', 
            message: error.message 
        });
    }
});

/**
 * 获取数据摘要
 */
router.get('/summary/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 7 } = req.query;
        
        const summary = dataService.getDataSummary(novelId, parseInt(days));
        
        if (!summary) {
            return res.status(404).json({ error: '暂无数据' });
        }
        
        res.json({
            success: true,
            data: summary,
            meta: {
                novelId,
                days: parseInt(days),
                generatedAt: new Date()
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '获取数据摘要失败', 
            message: error.message 
        });
    }
});

/**
 * 获取趋势分析
 */
router.get('/trend/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 30 } = req.query;
        
        const trendAnalysis = dataService.getTrendAnalysis(novelId, parseInt(days));
        
        res.json({
            success: true,
            data: trendAnalysis,
            meta: {
                novelId,
                days: parseInt(days),
                analyzedAt: new Date()
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '趋势分析失败', 
            message: error.message 
        });
    }
});

/**
 * 获取用户画像分析
 */
router.post('/user-profile/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const userData = req.body;
        
        // 获取历史数据作为用户行为分析的基础
        const historicalData = dataService.getHistoricalData(novelId, 30);
        const summary = dataService.getDataSummary(novelId, 30);
        
        const analysisData = {
            novelId,
            userData,
            historicalData: historicalData.slice(-7), // 最近7天数据
            summary
        };
        
        const userProfile = await aiService.analyzeUserProfile(analysisData);
        
        res.json({
            success: true,
            data: {
                profile: userProfile,
                analysisData: {
                    dataPoints: historicalData.length,
                    analysisDate: new Date()
                }
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '用户画像分析失败', 
            message: error.message 
        });
    }
});

/**
 * 综合数据分析
 */
router.get('/comprehensive/:novelId', async (req, res) => {
    try {
        const { novelId } = req.params;
        const { days = 30 } = req.query;
        
        // 获取各种分析数据
        const novelInfo = dataService.getNovelInfo(novelId);
        const historicalData = dataService.getHistoricalData(novelId, parseInt(days));
        const summary = dataService.getDataSummary(novelId, parseInt(days));
        const trendAnalysis = dataService.getTrendAnalysis(novelId, parseInt(days));
        
        if (!novelInfo) {
            return res.status(404).json({ error: '小说不存在' });
        }
        
        // 获取最新记录
        const latestRecord = historicalData.length > 0 ? historicalData[historicalData.length - 1] : null;

        const comprehensiveAnalysis = {
            novelInfo,
            summary,
            trendAnalysis,
            recentData: historicalData.slice(-7),
            latestRecord: latestRecord, // 添加最新记录
            dataHealth: {
                completeness: historicalData.length / parseInt(days),
                consistency: trendAnalysis.volatility < 0.3 ? 'good' : 'poor',
                freshness: historicalData.length > 0 ? 'current' : 'stale'
            }
        };
        
        res.json({
            success: true,
            data: comprehensiveAnalysis,
            meta: {
                novelId,
                days: parseInt(days),
                analysisDate: new Date(),
                dataPoints: historicalData.length
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '综合分析失败', 
            message: error.message 
        });
    }
});

/**
 * 数据对比分析
 */
router.post('/compare', async (req, res) => {
    try {
        const { novelIds, days = 30 } = req.body;
        
        if (!Array.isArray(novelIds) || novelIds.length < 2) {
            return res.status(400).json({ error: '需要至少两个小说ID进行对比' });
        }
        
        const comparisons = [];
        
        for (const novelId of novelIds) {
            const novelInfo = dataService.getNovelInfo(novelId);
            const summary = dataService.getDataSummary(novelId, parseInt(days));
            const trendAnalysis = dataService.getTrendAnalysis(novelId, parseInt(days));
            
            if (novelInfo && summary) {
                comparisons.push({
                    novelId,
                    novelInfo,
                    summary,
                    trendAnalysis
                });
            }
        }
        
        res.json({
            success: true,
            data: {
                comparisons,
                comparisonMetrics: {
                    bestPerformer: comparisons.reduce((best, current) => 
                        current.summary.totalReads > (best?.summary?.totalReads || 0) ? current : best
                    ),
                    fastestGrowing: comparisons.reduce((fastest, current) => {
                        const currentGrowth = parseFloat(current.summary.growthRates?.reads || 0);
                        const fastestGrowth = parseFloat(fastest?.summary?.growthRates?.reads || 0);
                        return currentGrowth > fastestGrowth ? current : fastest;
                    })
                }
            },
            meta: {
                comparedNovels: comparisons.length,
                days: parseInt(days),
                comparisonDate: new Date()
            }
        });
    } catch (error) {
        res.status(500).json({ 
            error: '对比分析失败', 
            message: error.message 
        });
    }
});

module.exports = router;
